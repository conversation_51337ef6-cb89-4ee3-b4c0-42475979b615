import React, { useState, useEffect } from 'react'
import { ZoomIn, ZoomOut, RotateCw, Download, ExternalLink } from 'lucide-react'
import { FileItem, LearningProgress } from '../types'
import { formatFileSize, formatDate } from '../lib/utils'
import { Button } from './ui/button'
import { databaseService } from '../services/database'

interface ImageViewerProps {
  file: FileItem
  onProgressUpdate?: (progress: LearningProgress) => void
}

export const ImageViewer: React.FC<ImageViewerProps> = ({ file, onProgressUpdate }) => {
  const [scale, setScale] = useState<number | 'auto'>('auto')
  const [rotation, setRotation] = useState<number>(0)
  const [progress, setProgress] = useState<LearningProgress | null>(null)
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const [actualScale, setActualScale] = useState<number>(1.0)

  useEffect(() => {
    loadProgress()
    markAsViewed()
  }, [file.path])

  const loadProgress = async () => {
    try {
      const savedProgress = await databaseService.getLearningProgress(file.path)
      if (savedProgress) {
        setProgress(savedProgress)
      }
    } catch (error) {
      console.error('Failed to load progress:', error)
    }
  }

  const markAsViewed = async () => {
    const progressData: LearningProgress = {
      filePath: file.path,
      fileType: 'image',
      progress: 100, // Images are considered "viewed" when opened
      currentPosition: 1,
      totalDuration: 1,
      isCompleted: true,
      isMarkedAsLearned: progress?.isMarkedAsLearned || false,
      lastAccessed: new Date(),
      notes: progress?.notes || '',
    }

    try {
      await databaseService.saveLearningProgress(progressData)
      setProgress(progressData)
      onProgressUpdate?.(progressData)
    } catch (error) {
      console.error('Failed to save progress:', error)
    }
  }

  const markAsLearned = async () => {
    if (!progress) return

    const updatedProgress = {
      ...progress,
      isMarkedAsLearned: true,
    }

    try {
      await databaseService.saveLearningProgress(updatedProgress)
      setProgress(updatedProgress)
      onProgressUpdate?.(updatedProgress)
    } catch (error) {
      console.error('Failed to mark as learned:', error)
    }
  }

  const zoomIn = () => {
    setScale(prev => {
      const currentScale = typeof prev === 'number' ? prev : actualScale
      return Math.min(5, currentScale + 0.2)
    })
  }

  const zoomOut = () => {
    setScale(prev => {
      const currentScale = typeof prev === 'number' ? prev : actualScale
      return Math.max(0.1, currentScale - 0.2)
    })
  }

  const resetZoom = () => {
    setScale('auto')
  }

  const rotate = () => {
    setRotation(prev => (prev + 90) % 360)
  }

  const openInExternal = async () => {
    try {
      await window.electronAPI.openExternal(`file://${file.path}`)
    } catch (error) {
      console.error('Failed to open external:', error)
    }
  }

  const showInFolder = async () => {
    try {
      await window.electronAPI.showItemInFolder(file.path)
    } catch (error) {
      console.error('Failed to show in folder:', error)
    }
  }

  if (imageError) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-muted-foreground">
          <div className="text-4xl mb-4">🖼️</div>
          <div className="mb-2">无法加载图片</div>
          <div className="text-sm">{file.name}</div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-container-full">
      {/* Fixed Toolbar */}
      <div className="flex-shrink-0 flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={zoomOut}>
            <ZoomOut className="w-4 h-4" />
          </Button>

          <span className="text-sm text-muted-foreground min-w-[60px] text-center">
            {scale === 'auto' ? 'Auto' : `${Math.round((typeof scale === 'number' ? scale : actualScale) * 100)}%`}
          </span>

          <Button variant="outline" size="sm" onClick={zoomIn}>
            <ZoomIn className="w-4 h-4" />
          </Button>

          <Button variant="outline" size="sm" onClick={resetZoom}>
            重置
          </Button>

          <Button variant="outline" size="sm" onClick={rotate}>
            <RotateCw className="w-4 h-4" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={showInFolder}>
            <ExternalLink className="w-4 h-4 mr-2" />
            在文件夹中显示
          </Button>

          <Button variant="outline" size="sm" onClick={openInExternal}>
            <Download className="w-4 h-4 mr-2" />
            用外部程序打开
          </Button>

          {progress && !progress.isMarkedAsLearned && (
            <Button variant="default" size="sm" onClick={markAsLearned}>
              标记为已学
            </Button>
          )}
        </div>
      </div>

      {/* Scrollable Image Content */}
      <div className="flex-1 scrollable-container bg-gray-100 flex items-center justify-center">
        <div className="p-4">
          <img
            src={(() => {
              const isBrowser = typeof window !== 'undefined' && !window.electronAPI?.readDirectory
              if (isBrowser) {
                // Use sample image for browser development
                return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vZGUuanMg5p625p6E5Zu+PC90ZXh0Pgo8L3N2Zz4K'
              }
              return `file://${file.path}`
            })()}
            alt={file.name}
            style={{
              transform: scale === 'auto'
                ? `rotate(${rotation}deg)`
                : `scale(${scale}) rotate(${rotation}deg)`,
              transition: 'transform 0.2s ease-in-out',
              maxWidth: scale === 'auto' ? '100%' : 'none',
              maxHeight: scale === 'auto' ? '100%' : 'none',
              objectFit: scale === 'auto' ? 'contain' : 'initial',
            }}
            onLoad={(e) => {
              setImageLoaded(true)
              if (scale === 'auto') {
                const img = e.target as HTMLImageElement
                const container = img.parentElement?.parentElement
                if (container) {
                  const containerWidth = container.clientWidth - 32 // padding
                  const containerHeight = container.clientHeight - 32
                  const scaleX = containerWidth / img.naturalWidth
                  const scaleY = containerHeight / img.naturalHeight
                  setActualScale(Math.min(scaleX, scaleY, 1))
                }
              }
            }}
            onError={() => setImageError(true)}
            className="shadow-lg"
          />
        </div>
      </div>
    </div>
  )
}
