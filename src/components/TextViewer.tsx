import React, { useState, useEffect } from 'react'
import { ExternalLink, Type, Search } from 'lucide-react'
import { FileItem, LearningProgress } from '../types'
import { formatFileSize, formatDate } from '../lib/utils'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { databaseService } from '../services/database'

interface TextViewerProps {
  file: FileItem
  onProgressUpdate?: (progress: LearningProgress) => void
}

export const TextViewer: React.FC<TextViewerProps> = ({ file, onProgressUpdate }) => {
  const [content, setContent] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [fontSize, setFontSize] = useState(14)
  const [searchTerm, setSearchTerm] = useState('')
  const [progress, setProgress] = useState<LearningProgress | null>(null)

  useEffect(() => {
    loadFile()
    loadProgress()
  }, [file.path])

  const loadFile = async () => {
    try {
      setLoading(true)
      setError(null)

      // In browser environment, use sample content
      const isBrowser = typeof window !== 'undefined' && !window.electronAPI?.readDirectory

      if (isBrowser) {
        const { sampleTextContent } = await import('../mock/sampleContent')
        setContent(sampleTextContent)
      } else {
        // Read file content using fetch with file:// protocol
        const response = await fetch(`file://${file.path}`)
        if (!response.ok) {
          throw new Error('Failed to load file')
        }

        const text = await response.text()
        setContent(text)
      }

      markAsViewed()
    } catch (error) {
      console.error('Failed to load file:', error)
      setError('无法加载文件内容')
    } finally {
      setLoading(false)
    }
  }

  const loadProgress = async () => {
    try {
      const savedProgress = await databaseService.getLearningProgress(file.path)
      if (savedProgress) {
        setProgress(savedProgress)
      }
    } catch (error) {
      console.error('Failed to load progress:', error)
    }
  }

  const markAsViewed = async () => {
    const progressData: LearningProgress = {
      filePath: file.path,
      fileType: 'text',
      progress: 100, // Text files are considered "read" when opened
      currentPosition: 1,
      totalDuration: 1,
      isCompleted: true,
      isMarkedAsLearned: progress?.isMarkedAsLearned || false,
      lastAccessed: new Date(),
      notes: progress?.notes || '',
    }

    try {
      await databaseService.saveLearningProgress(progressData)
      setProgress(progressData)
      onProgressUpdate?.(progressData)
    } catch (error) {
      console.error('Failed to save progress:', error)
    }
  }

  const markAsLearned = async () => {
    if (!progress) return

    const updatedProgress = {
      ...progress,
      isMarkedAsLearned: true,
    }

    try {
      await databaseService.saveLearningProgress(updatedProgress)
      setProgress(updatedProgress)
      onProgressUpdate?.(updatedProgress)
    } catch (error) {
      console.error('Failed to mark as learned:', error)
    }
  }

  const openInExternal = async () => {
    try {
      await window.electronAPI.openExternal(`file://${file.path}`)
    } catch (error) {
      console.error('Failed to open external:', error)
    }
  }

  const showInFolder = async () => {
    try {
      await window.electronAPI.showItemInFolder(file.path)
    } catch (error) {
      console.error('Failed to show in folder:', error)
    }
  }

  const highlightSearchTerm = (text: string, searchTerm: string) => {
    if (!searchTerm) return text

    const regex = new RegExp(`(${searchTerm})`, 'gi')
    return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>')
  }

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <div>加载文件中...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-red-500">
          <div className="text-4xl mb-4">📄</div>
          <div className="mb-2">{error}</div>
          <div className="text-sm">{file.name}</div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-container-full">
      {/* Fixed Toolbar */}
      <div className="flex-shrink-0 flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <Type className="w-4 h-4" />
            <input
              type="range"
              min="10"
              max="24"
              value={fontSize}
              onChange={(e) => setFontSize(parseInt(e.target.value))}
              className="w-20"
            />
            <span className="text-sm text-muted-foreground min-w-[40px]">
              {fontSize}px
            </span>
          </div>

          <div className="relative ml-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="搜索文本..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-48"
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={showInFolder}>
            <ExternalLink className="w-4 h-4 mr-2" />
            在文件夹中显示
          </Button>

          <Button variant="outline" size="sm" onClick={openInExternal}>
            用外部程序打开
          </Button>

          {progress && !progress.isMarkedAsLearned && (
            <Button variant="default" size="sm" onClick={markAsLearned}>
              标记为已学
            </Button>
          )}
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 scrollable-container p-4 pt-8 bg-background">
        <pre
          className="whitespace-pre-wrap font-mono leading-relaxed"
          style={{ fontSize: `${fontSize}px` }}
          dangerouslySetInnerHTML={{
            __html: highlightSearchTerm(content, searchTerm)
          }}
        />
      </div>

      {/* Fixed File Info Footer */}
      <div className="flex-shrink-0 p-4 border-t bg-background">
        <div className="flex items-center justify-between">
          <div>
            <div className="font-medium">{file.name}</div>
            <div className="text-sm text-muted-foreground">
              {formatFileSize(file.size)} • {formatDate(new Date(file.modified))} • {content.split('\n').length} 行
            </div>
          </div>

          {progress?.isMarkedAsLearned && (
            <span className="text-green-600 text-sm">✓ 已学习</span>
          )}
        </div>
      </div>
    </div>
  )
}
