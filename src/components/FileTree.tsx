import React, { useState, useEffect, createElement } from 'react'
import { ChevronRight, ChevronDown, Search } from 'lucide-react'
import { FaFolder } from 'react-icons/fa'
import { FileItem } from '../types'
import { formatFileSize, formatDate, getFileIcon, cn } from '../lib/utils'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { databaseService } from '../services/database'

interface FileTreeProps {
  currentPath: string | null
  onPathChange: (path: string) => void
  onFileSelect: (file: FileItem) => void
  selectedFile: FileItem | null
  refreshTrigger?: number // 用于触发进度数据刷新
}

interface FileTreeItemProps {
  item: FileItem
  onSelect: (item: FileItem) => void
  onToggle: (path: string) => void
  isSelected: boolean
  isExpanded: boolean
  level: number
  progress?: number
  isLearned?: boolean
  isDirLearned?: boolean
}

const FileTreeItem: React.FC<FileTreeItemProps> = ({
  item,
  onSelect,
  onToggle,
  isSelected,
  isExpanded,
  level,
  progress = 0,
  isLearned = false,
  isDirLearned = false,
}) => {
  const handleClick = () => {
    if (item.isDirectory) {
      onToggle(item.path)
    } else {
      onSelect(item)
    }
  }

  const IconComponent = getFileIcon(item.extension, item.isDirectory, isExpanded)

  return (
    <div
      className={cn(
        "flex items-center gap-2 px-2 py-1 cursor-pointer hover:bg-accent rounded-sm text-sm",
        isSelected && "bg-accent",
        (isLearned || (item.isDirectory && isDirLearned)) && "bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-300"
      )}
      style={{ paddingLeft: `${level * 16 + 8}px` }}
      onClick={handleClick}
    >
      {item.isDirectory && (
        <div className="w-4 h-4 flex items-center justify-center">
          {isExpanded ? (
            <ChevronDown className="w-3 h-3" />
          ) : (
            <ChevronRight className="w-3 h-3" />
          )}
        </div>
      )}

      <div className="w-4 h-4 flex items-center justify-center text-muted-foreground">
        {createElement(IconComponent, { className: "w-3.5 h-3.5" })}
      </div>

      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="truncate">{item.name}</span>
          {(isLearned || (item.isDirectory && isDirLearned)) && <span className="text-xs text-green-600">✓</span>}
        </div>

        {/* {!item.isDirectory && progress > 0 && (
          <Progress value={progress} className="h-1 mt-1" />
        )} */}

        {!item.isDirectory && (
          <div className="text-xs text-muted-foreground mt-1">
            {formatFileSize(item.size)} • {formatDate(new Date(item.modified))}
          </div>
        )}
      </div>
    </div>
  )
}

export const FileTree: React.FC<FileTreeProps> = ({
  currentPath,
  onPathChange,
  onFileSelect,
  selectedFile,
  refreshTrigger,
}) => {
  const [files, setFiles] = useState<FileItem[]>([])
  const [expandedDirs, setExpandedDirs] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')
  const [progressMap, setProgressMap] = useState<Map<string, number>>(new Map())
  const [learnedMap, setLearnedMap] = useState<Map<string, boolean>>(new Map())
  const [dirLearnedMap, setDirLearnedMap] = useState<Map<string, boolean>>(new Map())
  const [loading, setLoading] = useState(false)
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (currentPath) {
      loadDirectory(currentPath)
      loadProgressData()
    }
  }, [currentPath])

  // 监听进度更新触发器
  useEffect(() => {
    if (refreshTrigger !== undefined) {
      // 保存当前滚动位置
      const scrollTop = scrollContainerRef.current?.scrollTop || 0

      loadProgressData().then(() => {
        // 恢复滚动位置
        if (scrollContainerRef.current) {
          scrollContainerRef.current.scrollTop = scrollTop
        }
      })
    }
  }, [refreshTrigger])

  // 系统文件和隐藏文件过滤列表
  const HIDDEN_FILES = [
    '.DS_Store',
    'Thumbs.db',
    'desktop.ini',
    '.localized',
    '.fseventsd',
    '.Spotlight-V100',
    '.Trashes',
    '.TemporaryItems',
    '.DocumentRevisions-V100',
    '.VolumeIcon.icns',
    '.com.apple.timemachine.donotpresent',
    '.AppleDouble',
    '.LSOverride',
    '.AppleDB',
    '.AppleDesktop',
    'Network Trash Folder',
    'Temporary Items',
    '.apdisk'
  ]

  const shouldHideFile = (fileName: string): boolean => {
    // 隐藏以点开头的文件（除了一些常见的开发文件）
    if (fileName.startsWith('.')) {
      const allowedDotFiles = ['.gitignore', '.env', '.env.example', '.editorconfig', '.prettierrc', '.eslintrc']
      return !allowedDotFiles.some(allowed => fileName.startsWith(allowed))
    }

    // 隐藏系统文件
    return HIDDEN_FILES.includes(fileName)
  }

  const loadDirectory = async (path: string) => {
    try {
      setLoading(true)
      const items = await window.electronAPI.readDirectory(path)
      // 过滤掉系统文件和隐藏文件
      const filteredItems = items.filter(item => !shouldHideFile(item.name))
      setFiles(filteredItems)
    } catch (error) {
      console.error('Failed to load directory:', error)
      setFiles([])
    } finally {
      setLoading(false)
    }
  }

  const loadProgressData = async () => {
    try {
      const allProgress = await databaseService.getAllLearningProgress()
      const progressMap = new Map<string, number>()
      const learnedMap = new Map<string, boolean>()

      allProgress.forEach(progress => {
        progressMap.set(progress.filePath, progress.progress)
        learnedMap.set(progress.filePath, progress.isCompleted || progress.isMarkedAsLearned)
      })

      setProgressMap(progressMap)
      setLearnedMap(learnedMap)

      // Calculate directory learned status
      await calculateDirectoryLearnedStatus()
    } catch (error) {
      console.error('Failed to load progress data:', error)
    }
  }

  const calculateDirectoryLearnedStatus = async () => {
    if (!currentPath) return

    try {
      const dirLearnedMap = new Map<string, boolean>()

      // Get all directories in the current path
      const items = await window.electronAPI.readDirectory(currentPath)
      const directories = items.filter(item => item.isDirectory)

      for (const dir of directories) {
        const isDirLearned = await checkDirectoryLearned(dir.path)
        dirLearnedMap.set(dir.path, isDirLearned)
      }

      setDirLearnedMap(dirLearnedMap)
    } catch (error) {
      console.error('Failed to calculate directory learned status:', error)
    }
  }

  // Check if all files in a directory are learned (async version for detailed checking)
  const checkDirectoryLearned = async (dirPath: string): Promise<boolean> => {
    try {
      const items = await window.electronAPI.readDirectory(dirPath)
      let allLearned = true
      let hasFiles = false

      for (const item of items) {
        if (item.isDirectory) {
          // Recursively check subdirectories
          const subDirLearned = await checkDirectoryLearned(item.path)
          if (!subDirLearned) {
            allLearned = false
          }
        } else {
          hasFiles = true
          const isLearned = learnedMap.get(item.path) || false
          if (!isLearned) {
            allLearned = false
          }
        }
      }

      // If directory has no files (only subdirectories), check if all subdirectories are learned
      return hasFiles ? allLearned : allLearned
    } catch (error) {
      console.error('Failed to check directory learned status:', error)
      return false
    }
  }

  const handleSelectDirectory = async () => {
    try {
      const selectedPath = await window.electronAPI.selectDirectory()
      if (selectedPath) {
        onPathChange(selectedPath)

        // Save directory state
        await databaseService.saveDirectoryState({
          path: selectedPath,
          lastAccessed: new Date(),
          isBookmarked: false,
        })

        // Update last opened directory setting
        await databaseService.setSetting('lastOpenedDirectory', selectedPath)
      }
    } catch (error) {
      console.error('Failed to select directory:', error)
    }
  }

  const handleToggleDirectory = async (path: string) => {
    const newExpanded = new Set(expandedDirs)
    if (newExpanded.has(path)) {
      newExpanded.delete(path)
    } else {
      newExpanded.add(path)
    }
    setExpandedDirs(newExpanded)
  }

  const filteredFiles = files.filter(file =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const renderFileTree = (items: FileItem[], level: number = 0): React.ReactNode => {
    return items.map(item => {
      const isDirLearned = item.isDirectory ? (dirLearnedMap.get(item.path) || false) : false

      return (
        <div key={item.path}>
          <FileTreeItem
            item={item}
            onSelect={onFileSelect}
            onToggle={handleToggleDirectory}
            isSelected={selectedFile?.path === item.path}
            isExpanded={expandedDirs.has(item.path)}
            level={level}
            progress={progressMap.get(item.path) || 0}
            isLearned={learnedMap.get(item.path) || false}
            isDirLearned={isDirLearned}
          />

          {item.isDirectory && expandedDirs.has(item.path) && (
            <DirectoryContents path={item.path} level={level + 1} />
          )}
        </div>
      )
    })
  }

  const DirectoryContents: React.FC<{ path: string; level: number }> = ({ path, level }) => {
    const [subFiles, setSubFiles] = useState<FileItem[]>([])

    useEffect(() => {
      const loadSubDirectory = async () => {
        try {
          const items = await window.electronAPI.readDirectory(path)
          // 过滤掉系统文件和隐藏文件
          const filteredItems = items.filter(item => !shouldHideFile(item.name))
          setSubFiles(filteredItems)
        } catch (error) {
          console.error('Failed to load subdirectory:', error)
          setSubFiles([])
        }
      }

      loadSubDirectory()
    }, [path])

    const renderSubFileTree = (items: FileItem[], level: number): React.ReactNode => {
      return items.map(item => {
        const isDirLearned = item.isDirectory ? (dirLearnedMap.get(item.path) || false) : false

        return (
          <div key={item.path}>
            <FileTreeItem
              item={item}
              onSelect={onFileSelect}
              onToggle={handleToggleDirectory}
              isSelected={selectedFile?.path === item.path}
              isExpanded={expandedDirs.has(item.path)}
              level={level}
              progress={progressMap.get(item.path) || 0}
              isLearned={learnedMap.get(item.path) || false}
              isDirLearned={isDirLearned}
            />

            {item.isDirectory && expandedDirs.has(item.path) && (
              <DirectoryContents path={item.path} level={level + 1} />
            )}
          </div>
        )
      })
    }

    return <>{renderSubFileTree(subFiles, level)}</>
  }

  return (
    <div className="flex-container-full">
      {/* Fixed Header - Draggable */}
      <div
        className="flex-shrink-0 p-4 border-b bg-card select-none"
        style={{ WebkitAppRegion: 'drag' as any }}
      >
        <div
          className="flex items-center justify-end mb-3"
          style={{ WebkitAppRegion: 'drag' as any }}
        >
          <Button
            onClick={handleSelectDirectory}
            variant="outline"
            size="sm"
            style={{ WebkitAppRegion: 'no-drag' as any }}
          >
            <FaFolder className="w-4 h-4 mr-2" />
            选择目录
          </Button>
        </div>

        {currentPath && (
          <div
            className="text-xs text-muted-foreground mb-3 break-all"
            style={{ WebkitAppRegion: 'drag' as any }}
          >
            {currentPath}
          </div>
        )}

        <div
          className="relative"
          style={{ WebkitAppRegion: 'no-drag' as any }}
        >
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="搜索文件..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Scrollable File Tree */}
      <div ref={scrollContainerRef} className="flex-1 scrollable-container">
        {loading ? (
          <div className="p-4 text-center text-muted-foreground">
            加载中...
          </div>
        ) : currentPath ? (
          <div className="p-2">
            {renderFileTree(filteredFiles)}
          </div>
        ) : (
          <div className="p-4 text-center text-muted-foreground">
            请选择一个目录开始浏览
          </div>
        )}
      </div>
    </div>
  )
}
