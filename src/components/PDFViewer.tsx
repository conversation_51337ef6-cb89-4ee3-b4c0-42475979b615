import React, { useState, useEffect } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCw } from 'lucide-react'
import { FileItem, LearningProgress } from '../types'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { databaseService } from '../services/database'

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`

interface PDFViewerProps {
  file: FileItem
  onProgressUpdate?: (progress: LearningProgress) => void
}

export const PDFViewer: React.FC<PDFViewerProps> = ({ file, onProgressUpdate }) => {
  const [numPages, setNumPages] = useState<number>(0)
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [scale, setScale] = useState<number | 'auto'>('auto')
  const [rotation, setRotation] = useState<number>(0)
  const [progress, setProgress] = useState<LearningProgress | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [actualScale, setActualScale] = useState<number>(1.0)

  useEffect(() => {
    loadProgress()
  }, [file.path])

  useEffect(() => {
    if (numPages > 0) {
      saveProgress()
    }
  }, [pageNumber, numPages])

  const loadProgress = async () => {
    try {
      const savedProgress = await databaseService.getLearningProgress(file.path)
      if (savedProgress && savedProgress.currentPosition) {
        setProgress(savedProgress)
        setPageNumber(savedProgress.currentPosition)
      }
    } catch (error) {
      console.error('Failed to load progress:', error)
    }
  }

  const saveProgress = async () => {
    if (!numPages || numPages === 0) return

    const progressPercentage = (pageNumber / numPages) * 100
    const isCompleted = progressPercentage >= 95 // Consider 95% as completed

    const progressData: LearningProgress = {
      filePath: file.path,
      fileType: 'pdf',
      progress: progressPercentage,
      currentPosition: pageNumber,
      totalDuration: numPages,
      isCompleted,
      isMarkedAsLearned: progress?.isMarkedAsLearned || false,
      lastAccessed: new Date(),
      notes: progress?.notes || '',
    }

    try {
      await databaseService.saveLearningProgress(progressData)
      setProgress(progressData)
      onProgressUpdate?.(progressData)
    } catch (error) {
      console.error('Failed to save progress:', error)
    }
  }

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages)
    setLoading(false)
    setError(null)
  }

  const onDocumentLoadError = (error: Error) => {
    setError('Failed to load PDF: ' + error.message)
    setLoading(false)
  }

  const goToPrevPage = () => {
    setPageNumber(prev => Math.max(1, prev - 1))
  }

  const goToNextPage = () => {
    setPageNumber(prev => Math.min(numPages, prev + 1))
  }

  const goToPage = (page: number) => {
    const pageNum = Math.max(1, Math.min(numPages, page))
    setPageNumber(pageNum)
  }

  const zoomIn = () => {
    setScale(prev => {
      const currentScale = typeof prev === 'number' ? prev : actualScale
      return Math.min(3, currentScale + 0.2)
    })
  }

  const zoomOut = () => {
    setScale(prev => {
      const currentScale = typeof prev === 'number' ? prev : actualScale
      return Math.max(0.5, currentScale - 0.2)
    })
  }

  const resetZoom = () => {
    setScale('auto')
  }

  const rotate = () => {
    setRotation(prev => (prev + 90) % 360)
  }

  const markAsCompleted = async () => {
    if (!progress) return

    const updatedProgress = {
      ...progress,
      isCompleted: true,
      isMarkedAsLearned: true,
      progress: 100,
    }

    try {
      await databaseService.saveLearningProgress(updatedProgress)
      setProgress(updatedProgress)
      onProgressUpdate?.(updatedProgress)
    } catch (error) {
      console.error('Failed to mark as completed:', error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <div>加载 PDF 中...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center text-red-500">
          <div className="mb-2">❌</div>
          <div>{error}</div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-container-full">
      {/* Fixed Toolbar */}
      <div className="flex-shrink-0 flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={goToPrevPage}
            disabled={pageNumber <= 1}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>

          <div className="flex items-center gap-2">
            <Input
              type="number"
              value={pageNumber}
              onChange={(e) => goToPage(parseInt(e.target.value) || 1)}
              className="w-16 text-center"
              min={1}
              max={numPages}
            />
            <span className="text-sm text-muted-foreground">/ {numPages}</span>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={goToNextPage}
            disabled={pageNumber >= numPages}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={zoomOut}>
            <ZoomOut className="w-4 h-4" />
          </Button>

          <span className="text-sm text-muted-foreground min-w-[60px] text-center">
            {scale === 'auto' ? 'Auto' : `${Math.round((typeof scale === 'number' ? scale : actualScale) * 100)}%`}
          </span>

          <Button variant="outline" size="sm" onClick={zoomIn}>
            <ZoomIn className="w-4 h-4" />
          </Button>

          <Button variant="outline" size="sm" onClick={resetZoom}>
            重置
          </Button>

          <Button variant="outline" size="sm" onClick={rotate}>
            <RotateCw className="w-4 h-4" />
          </Button>

          {progress && !progress.isCompleted && (
            <Button variant="default" size="sm" onClick={markAsCompleted}>
              标记为已学
            </Button>
          )}
        </div>
      </div>

      {/* PDF Content */}
      <div className="flex-1 scrollable-container bg-gray-100 flex justify-center">
        <div className="p-4 pt-8">
          <Document
            file={(() => {
              const isBrowser = typeof window !== 'undefined' && !window.electronAPI?.readDirectory
              if (isBrowser) {
                // Use sample PDF for browser development
                return 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf'
              }
              return `file://${file.path}`
            })()}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            }
          >
            <Page
              pageNumber={pageNumber}
              scale={scale === 'auto' ? undefined : scale}
              rotate={rotation}
              loading={
                <div className="flex items-center justify-center h-64 bg-white border">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              }
              className="shadow-lg"
            />
          </Document>
        </div>
      </div>


    </div>
  )
}
