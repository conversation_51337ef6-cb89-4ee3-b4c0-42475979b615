import React, { useState, useEffect } from 'react'
import { FileItem, LearningProgress } from '../types'
import { databaseService } from '../services/database'

interface PDFViewerProps {
  file: FileItem
  onProgressUpdate?: (progress: LearningProgress) => void
}

export const PDFViewer: React.FC<PDFViewerProps> = ({ file, onProgressUpdate }) => {
  const [progress, setProgress] = useState<LearningProgress | null>(null)

  useEffect(() => {
    loadProgress()
    markAsViewed()
  }, [file.path])

  const loadProgress = async () => {
    try {
      const savedProgress = await databaseService.getLearningProgress(file.path)
      if (savedProgress) {
        setProgress(savedProgress)
      }
    } catch (error) {
      console.error('Failed to load progress:', error)
    }
  }

  const markAsViewed = async () => {
    const progressData: LearningProgress = {
      filePath: file.path,
      fileType: 'pdf',
      progress: 100, // 简单标记为已查看
      isCompleted: true,
      isMarkedAsLearned: progress?.isMarkedAsLearned || false,
      lastAccessed: new Date(),
      notes: progress?.notes || '',
    }

    try {
      await databaseService.saveLearningProgress(progressData)
      setProgress(progressData)
      onProgressUpdate?.(progressData)
    } catch (error) {
      console.error('Failed to save progress:', error)
    }
  }

  const getPdfUrl = () => {
    const isBrowser = typeof window !== 'undefined' && !window.electronAPI?.readDirectory
    if (isBrowser) {
      // Use sample PDF for browser development
      return 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf'
    }
    return `file://${file.path}`
  }

  return (
    <div className="flex-container-full">
      {/* PDF Content via iframe */}
      <div className="flex-1 scrollable-container bg-gray-100">
        <div className="p-4 pt-8 h-full">
          <iframe
            src={getPdfUrl()}
            className="w-full h-full border-0 rounded shadow-lg"
            title={`PDF: ${file.name}`}
            style={{ minHeight: 'calc(100vh - 200px)' }}
          />
        </div>
      </div>
    </div>
  )
}