import React, { useState, useRef, useEffect } from 'react'
import { Play, Pause, Volume2, VolumeX, Maximize, Setting<PERSON>, <PERSON><PERSON><PERSON><PERSON>, SkipForward } from 'lucide-react'
import { FileItem, LearningProgress, getFileType } from '../types'
import { formatDuration, cn } from '../lib/utils'
import { Button } from './ui/button'
import { Slider } from './ui/slider'
import { databaseService } from '../services/database'

interface VideoPlayerProps {
  file: FileItem
  onProgressUpdate?: (progress: LearningProgress) => void
}

export const VideoPlayer: React.FC<VideoPlayerProps> = ({ file, onProgressUpdate }) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [playbackSpeed, setPlaybackSpeed] = useState(1)
  const [showControls, setShowControls] = useState(true)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [progress, setProgress] = useState<LearningProgress | null>(null)
  const [isVideoFocused, setIsVideoFocused] = useState(false)

  const controlsTimeoutRef = useRef<NodeJS.Timeout>()
  const videoContainerRef = useRef<HTMLDivElement>(null)
  const lastSavedTimeRef = useRef<number>(0)
  const saveProgressTimeoutRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    loadProgress()
    loadSettings()
  }, [file.path])

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
      // 节流保存进度，避免频繁更新
      debouncedSaveProgress(video.currentTime, video.duration)
    }

    const handleDurationChange = () => {
      setDuration(video.duration)
    }

    const handleEnded = () => {
      setIsPlaying(false)
      markAsCompleted()
    }

    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('durationchange', handleDurationChange)
    video.addEventListener('ended', handleEnded)

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('durationchange', handleDurationChange)
      video.removeEventListener('ended', handleEnded)
    }
  }, [file.path])

  const loadProgress = async () => {
    try {
      const savedProgress = await databaseService.getLearningProgress(file.path)
      if (savedProgress) {
        setProgress(savedProgress)
        if (videoRef.current && savedProgress.currentPosition) {
          videoRef.current.currentTime = savedProgress.currentPosition
        }
      }
    } catch (error) {
      console.error('Failed to load progress:', error)
    }
  }

  const loadSettings = async () => {
    try {
      const speed = await databaseService.getSetting('videoPlaybackSpeed')
      if (speed) {
        const speedValue = parseFloat(speed)
        setPlaybackSpeed(speedValue)
        if (videoRef.current) {
          videoRef.current.playbackRate = speedValue
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }

  const saveProgress = async (currentTime: number, totalDuration: number) => {
    if (!totalDuration || totalDuration === 0) return

    const progressPercentage = (currentTime / totalDuration) * 100
    const isCompleted = progressPercentage >= 95 // Consider 95% as completed

    const progressData: LearningProgress = {
      filePath: file.path,
      fileType: 'video',
      progress: progressPercentage,
      currentPosition: currentTime,
      totalDuration: totalDuration,
      isCompleted,
      isMarkedAsLearned: progress?.isMarkedAsLearned || false,
      lastAccessed: new Date(),
      notes: progress?.notes || '',
    }

    try {
      await databaseService.saveLearningProgress(progressData)
      setProgress(progressData)
      onProgressUpdate?.(progressData)
    } catch (error) {
      console.error('Failed to save progress:', error)
    }
  }

  const markAsCompleted = async () => {
    if (!progress) return

    const updatedProgress = {
      ...progress,
      isCompleted: true,
      isMarkedAsLearned: true,
      progress: 100,
    }

    try {
      await databaseService.saveLearningProgress(updatedProgress)
      setProgress(updatedProgress)
      onProgressUpdate?.(updatedProgress)
    } catch (error) {
      console.error('Failed to mark as completed:', error)
    }
  }

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
    setIsPlaying(!isPlaying)
  }

  const handleSeek = (value: number[]) => {
    const video = videoRef.current
    if (!video || !duration) return

    const newTime = (value[0] / 100) * duration
    video.currentTime = newTime
    setCurrentTime(newTime)
  }

  const handleVolumeChange = (value: number[]) => {
    const video = videoRef.current
    if (!video) return

    const newVolume = value[0] / 100
    video.volume = newVolume
    setVolume(newVolume)
    setIsMuted(newVolume === 0)
  }

  const toggleMute = () => {
    const video = videoRef.current
    if (!video) return

    if (isMuted) {
      video.volume = volume
      setIsMuted(false)
    } else {
      video.volume = 0
      setIsMuted(true)
    }
  }

  const changePlaybackSpeed = async (speed: number) => {
    const video = videoRef.current
    if (!video) return

    video.playbackRate = speed
    setPlaybackSpeed(speed)

    try {
      await databaseService.setSetting('videoPlaybackSpeed', speed.toString())
    } catch (error) {
      console.error('Failed to save playback speed:', error)
    }
  }

  const skip = (seconds: number) => {
    const video = videoRef.current
    if (!video) return

    video.currentTime = Math.max(0, Math.min(video.currentTime + seconds, duration))
  }

  const toggleFullscreen = () => {
    const video = videoRef.current
    if (!video) return

    if (!isFullscreen) {
      if (video.requestFullscreen) {
        video.requestFullscreen()
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
    }
    setIsFullscreen(!isFullscreen)
  }

  const showControlsTemporarily = () => {
    setShowControls(true)
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }
    controlsTimeoutRef.current = setTimeout(() => {
      if (isVideoFocused && isPlaying) {
        setShowControls(false)
      }
    }, 3000)
  }

  const handleVideoFocus = () => {
    setIsVideoFocused(true)
  }

  const handleVideoBlur = () => {
    setIsVideoFocused(false)
    setShowControls(true)
  }

  const handleMouseEnter = () => {
    setShowControls(true)
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }
  }

  const handleMouseLeave = () => {
    if (isPlaying) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false)
      }, 1000)
    }
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isVideoFocused) return

      switch (e.code) {
        case 'Space':
          e.preventDefault()
          togglePlay()
          break
        case 'ArrowLeft':
          e.preventDefault()
          skip(-10)
          break
        case 'ArrowRight':
          e.preventDefault()
          skip(10)
          break
        case 'ArrowUp':
          e.preventDefault()
          handleVolumeChange([Math.min(100, volume * 100 + 10)])
          break
        case 'ArrowDown':
          e.preventDefault()
          handleVolumeChange([Math.max(0, volume * 100 - 10)])
          break
        case 'KeyM':
          e.preventDefault()
          toggleMute()
          break
        case 'KeyF':
          e.preventDefault()
          toggleFullscreen()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isVideoFocused, volume, isPlaying])

  const progressPercentage = duration ? (currentTime / duration) * 100 : 0

  return (
    <div className="flex-container-full">
      <div
        ref={videoContainerRef}
        className="relative flex-1 bg-black group"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onMouseMove={showControlsTemporarily}
        tabIndex={0}
        onFocus={handleVideoFocus}
        onBlur={handleVideoBlur}
      >
        <video
          ref={videoRef}
          src={(() => {
            const isBrowser = typeof window !== 'undefined' && !window.electronAPI?.readDirectory
            if (isBrowser) {
              // Use sample video for browser development
              return 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
            }
            return `file://${file.path}`
          })()}
          className="w-full h-full object-contain"
          onClick={togglePlay}
          crossOrigin="anonymous"
        />

        {/* Controls overlay */}
        <div className={cn(
          "absolute inset-0 bg-gradient-to-t from-black/50 to-transparent transition-opacity duration-300",
          showControls || !isPlaying ? "opacity-100" : "opacity-0"
        )}>
          {/* Progress bar */}
          <div className="absolute bottom-16 left-4 right-4">
            <Slider
              value={[progressPercentage]}
              onValueChange={handleSeek}
              max={100}
              step={0.1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-white mt-1">
              <span>{formatDuration(currentTime)}</span>
              <span>{formatDuration(duration)}</span>
            </div>
          </div>

          {/* Control buttons */}
          <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => skip(-10)}
                className="text-white hover:bg-white/20"
              >
                <SkipBack className="w-5 h-5" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                onClick={togglePlay}
                className="text-white hover:bg-white/20"
              >
                {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
              </Button>

              <Button
                variant="ghost"
                size="icon"
                onClick={() => skip(10)}
                className="text-white hover:bg-white/20"
              >
                <SkipForward className="w-5 h-5" />
              </Button>

              <div className="flex items-center gap-2 ml-4">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleMute}
                  className="text-white hover:bg-white/20"
                >
                  {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                </Button>

                <Slider
                  value={[isMuted ? 0 : volume * 100]}
                  onValueChange={handleVolumeChange}
                  max={100}
                  className="w-20"
                />
              </div>
            </div>

            <div className="flex items-center gap-2">
              <select
                value={playbackSpeed}
                onChange={(e) => changePlaybackSpeed(parseFloat(e.target.value))}
                className="bg-black/50 text-white border border-white/20 rounded px-2 py-1 text-sm"
              >
                <option value={0.5}>0.5x</option>
                <option value={0.75}>0.75x</option>
                <option value={1}>1x</option>
                <option value={1.25}>1.25x</option>
                <option value={1.5}>1.5x</option>
                <option value={2}>2x</option>
              </select>

              <Button
                variant="ghost"
                size="icon"
                onClick={toggleFullscreen}
                className="text-white hover:bg-white/20"
              >
                <Maximize className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Loading state */}
        {duration === 0 && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50">
            <div className="text-white">加载中...</div>
          </div>
        )}
      </div>
    </div>
  )
}
