import React, { useState, useRef, useEffect } from 'react'
import { Play, Pause, Volume2, VolumeX, Maximize, SkipBack, SkipForward } from 'lucide-react'
import { FileItem, LearningProgress } from '../types'
import { formatDuration, cn } from '../lib/utils'
import { Button } from './ui/button'
import { Slider } from './ui/slider'
import { databaseService } from '../services/database'

interface VideoPlayerProps {
  file: FileItem
  onProgressUpdate?: (progress: LearningProgress) => void
}

export const VideoPlayer: React.FC<VideoPlayerProps> = ({ file, onProgressUpdate }) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [playbackSpeed, setPlaybackSpeed] = useState(1)
  const [showControls, setShowControls] = useState(true)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [progress, setProgress] = useState<LearningProgress | null>(null)
  const [isVideoFocused, setIsVideoFocused] = useState(false)

  const controlsTimeoutRef = useRef<NodeJS.Timeout>()
  const videoContainerRef = useRef<HTMLDivElement>(null)
  const lastSavedTimeRef = useRef<number>(0)
  const saveProgressTimeoutRef = useRef<NodeJS.Timeout>()
  const mouseMoveTimeoutRef = useRef<NodeJS.Timeout>()
  const [isMouseMoving, setIsMouseMoving] = useState(false)

  useEffect(() => {
    loadProgress()
    loadSettings()
  }, [file.path])

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
      // 节流保存进度，避免频繁更新
      debouncedSaveProgress(video.currentTime, video.duration)
    }

    const handleDurationChange = () => {
      setDuration(video.duration)
    }

    const handleEnded = () => {
      setIsPlaying(false)
      markAsCompleted()
    }

    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('durationchange', handleDurationChange)
    video.addEventListener('ended', handleEnded)

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('durationchange', handleDurationChange)
      video.removeEventListener('ended', handleEnded)
    }
  }, [file.path])

  const loadProgress = async () => {
    try {
      const savedProgress = await databaseService.getLearningProgress(file.path)
      if (savedProgress) {
        setProgress(savedProgress)
        if (videoRef.current && savedProgress.currentPosition) {
          videoRef.current.currentTime = savedProgress.currentPosition
        }
      }
    } catch (error) {
      console.error('Failed to load progress:', error)
    }
  }

  const loadSettings = async () => {
    try {
      const speed = await databaseService.getSetting('videoPlaybackSpeed')
      if (speed) {
        const speedValue = parseFloat(speed)
        setPlaybackSpeed(speedValue)
        if (videoRef.current) {
          videoRef.current.playbackRate = speedValue
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }

  const saveProgress = async (currentTime: number, totalDuration: number) => {
    if (!totalDuration || totalDuration === 0) return

    const progressPercentage = (currentTime / totalDuration) * 100
    const isCompleted = progressPercentage >= 99.5 // Consider 99.5% as completed (essentially 100%)

    const progressData: LearningProgress = {
      filePath: file.path,
      fileType: 'video',
      progress: progressPercentage,
      currentPosition: currentTime,
      totalDuration: totalDuration,
      isCompleted,
      isMarkedAsLearned: progress?.isMarkedAsLearned || false,
      lastAccessed: new Date(),
      notes: progress?.notes || '',
    }

    try {
      await databaseService.saveLearningProgress(progressData)
      setProgress(progressData)
      onProgressUpdate?.(progressData)
    } catch (error) {
      console.error('Failed to save progress:', error)
    }
  }

  const markAsCompleted = async () => {
    if (!progress) return

    const updatedProgress = {
      ...progress,
      isCompleted: true,
      isMarkedAsLearned: true,
      progress: 100,
    }

    try {
      await databaseService.saveLearningProgress(updatedProgress)
      setProgress(updatedProgress)
      onProgressUpdate?.(updatedProgress)
    } catch (error) {
      console.error('Failed to mark as completed:', error)
    }
  }

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
    setIsPlaying(!isPlaying)
  }

  const handleSeek = (value: number[]) => {
    const video = videoRef.current
    if (!video || !duration) return

    const newTime = (value[0] / 100) * duration
    video.currentTime = newTime
    setCurrentTime(newTime)
  }

  const handleVolumeChange = (value: number[]) => {
    const video = videoRef.current
    if (!video) return

    const newVolume = value[0] / 100
    video.volume = newVolume
    setVolume(newVolume)
    setIsMuted(newVolume === 0)
  }

  const toggleMute = () => {
    const video = videoRef.current
    if (!video) return

    if (isMuted) {
      video.volume = volume
      setIsMuted(false)
    } else {
      video.volume = 0
      setIsMuted(true)
    }
  }

  const changePlaybackSpeed = async (speed: number) => {
    const video = videoRef.current
    if (!video) return

    video.playbackRate = speed
    setPlaybackSpeed(speed)

    try {
      await databaseService.setSetting('videoPlaybackSpeed', speed.toString())
    } catch (error) {
      console.error('Failed to save playback speed:', error)
    }
  }

  // 防抖保存进度，避免频繁更新导致目录树重新渲染
  const debouncedSaveProgress = (currentTime: number, totalDuration: number) => {
    // 清除之前的定时器
    if (saveProgressTimeoutRef.current) {
      clearTimeout(saveProgressTimeoutRef.current)
    }

    // 只有在时间变化超过5秒或者暂停时才保存进度
    const timeDiff = Math.abs(currentTime - lastSavedTimeRef.current)
    if (timeDiff < 5 && isPlaying) {
      return
    }

    // 设置新的定时器，延迟保存
    saveProgressTimeoutRef.current = setTimeout(() => {
      saveProgress(currentTime, totalDuration)
      lastSavedTimeRef.current = currentTime
    }, isPlaying ? 2000 : 500) // 播放时延迟2秒，暂停时延迟0.5秒
  }

  // 在组件卸载或文件切换时立即保存进度
  useEffect(() => {
    return () => {
      if (saveProgressTimeoutRef.current) {
        clearTimeout(saveProgressTimeoutRef.current)
      }
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
      if (mouseMoveTimeoutRef.current) {
        clearTimeout(mouseMoveTimeoutRef.current)
      }
      // 立即保存当前进度
      if (videoRef.current && videoRef.current.duration) {
        saveProgress(videoRef.current.currentTime, videoRef.current.duration)
      }
    }
  }, [file.path])

  // 暂停时立即保存进度
  useEffect(() => {
    if (!isPlaying && videoRef.current && videoRef.current.duration) {
      if (saveProgressTimeoutRef.current) {
        clearTimeout(saveProgressTimeoutRef.current)
      }
      saveProgress(videoRef.current.currentTime, videoRef.current.duration)
      lastSavedTimeRef.current = videoRef.current.currentTime
    }
  }, [isPlaying])

  const skip = (seconds: number) => {
    const video = videoRef.current
    if (!video) return

    video.currentTime = Math.max(0, Math.min(video.currentTime + seconds, duration))
  }

  const toggleFullscreen = () => {
    const video = videoRef.current
    if (!video) return

    if (!isFullscreen) {
      if (video.requestFullscreen) {
        video.requestFullscreen()
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
    }
    setIsFullscreen(!isFullscreen)
  }

  const hideControlsAfterDelay = () => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }

    // 只有在播放状态下才隐藏控制条
    if (isPlaying) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false)
      }, 3000) // 3秒后隐藏
    }
  }

  const handleMouseMove = () => {
    // 显示控制条
    setShowControls(true)
    setIsMouseMoving(true)

    // 清除之前的定时器
    if (mouseMoveTimeoutRef.current) {
      clearTimeout(mouseMoveTimeoutRef.current)
    }
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }

    // 设置鼠标停止移动的检测
    mouseMoveTimeoutRef.current = setTimeout(() => {
      setIsMouseMoving(false)
      // 鼠标停止移动后，再等待一段时间隐藏控制条
      hideControlsAfterDelay()
    }, 500) // 500ms后认为鼠标停止移动
  }

  const handleVideoFocus = () => {
    setIsVideoFocused(true)
  }

  const handleVideoBlur = () => {
    setIsVideoFocused(false)
    setShowControls(true)
  }

  const handleMouseEnter = () => {
    setShowControls(true)
    setIsMouseMoving(true)
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }
  }

  const handleMouseLeave = () => {
    setIsMouseMoving(false)
    // 清除所有定时器
    if (mouseMoveTimeoutRef.current) {
      clearTimeout(mouseMoveTimeoutRef.current)
    }
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }

    // 鼠标离开后立即开始隐藏倒计时
    if (isPlaying) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false)
      }, 1000) // 1秒后隐藏
    }
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isVideoFocused) return

      switch (e.code) {
        case 'Space':
          e.preventDefault()
          togglePlay()
          break
        case 'ArrowLeft':
          e.preventDefault()
          skip(-10)
          break
        case 'ArrowRight':
          e.preventDefault()
          skip(10)
          break
        case 'ArrowUp':
          e.preventDefault()
          handleVolumeChange([Math.min(100, volume * 100 + 10)])
          break
        case 'ArrowDown':
          e.preventDefault()
          handleVolumeChange([Math.max(0, volume * 100 - 10)])
          break
        case 'KeyM':
          e.preventDefault()
          toggleMute()
          break
        case 'KeyF':
          e.preventDefault()
          toggleFullscreen()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isVideoFocused, volume, isPlaying])

  const progressPercentage = duration ? (currentTime / duration) * 100 : 0

  return (
    <div className="flex-container-full">
      <div
        ref={videoContainerRef}
        className="relative flex-1 bg-black group"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onMouseMove={handleMouseMove}
        tabIndex={0}
        onFocus={handleVideoFocus}
        onBlur={handleVideoBlur}
      >
        <video
          ref={videoRef}
          src={(() => {
            const isBrowser = typeof window !== 'undefined' && !window.electronAPI?.readDirectory
            if (isBrowser) {
              // Use sample video for browser development
              return 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
            }
            return `file://${file.path}`
          })()}
          className="w-full h-full object-contain"
          onClick={togglePlay}
          onDoubleClick={togglePlay}
          crossOrigin="anonymous"
        />

        {/* Controls overlay - only show background in control area */}
        <div className={cn(
          "absolute inset-0 transition-opacity duration-300",
          showControls || !isPlaying ? "opacity-100" : "opacity-0"
        )}>
          {/* Control area background */}
          <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-black/70 to-transparent" />

          {/* Progress bar */}
          <div className="absolute bottom-16 left-4 right-4 z-10">
            <Slider
              value={[progressPercentage]}
              onValueChange={handleSeek}
              max={100}
              step={0.1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-white mt-1">
              <span>{formatDuration(currentTime)}</span>
              <span>{formatDuration(duration)}</span>
            </div>
          </div>

          {/* Control buttons */}
          <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between z-10">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => skip(-10)}
                className="text-white hover:bg-white/20"
              >
                <SkipBack className="w-5 h-5" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                onClick={togglePlay}
                className="text-white hover:bg-white/20"
              >
                {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
              </Button>

              <Button
                variant="ghost"
                size="icon"
                onClick={() => skip(10)}
                className="text-white hover:bg-white/20"
              >
                <SkipForward className="w-5 h-5" />
              </Button>

              <div className="flex items-center gap-2 ml-4">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleMute}
                  className="text-white hover:bg-white/20"
                >
                  {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                </Button>

                <Slider
                  value={[isMuted ? 0 : volume * 100]}
                  onValueChange={handleVolumeChange}
                  max={100}
                  className="w-20"
                />
              </div>
            </div>

            <div className="flex items-center gap-2">
              <select
                value={playbackSpeed}
                onChange={(e) => changePlaybackSpeed(parseFloat(e.target.value))}
                className="bg-black/50 text-white border border-white/20 rounded px-2 py-1 text-sm"
              >
                <option value={0.5}>0.5x</option>
                <option value={0.75}>0.75x</option>
                <option value={1}>1x</option>
                <option value={1.25}>1.25x</option>
                <option value={1.5}>1.5x</option>
                <option value={2}>2x</option>
              </select>

              <Button
                variant="ghost"
                size="icon"
                onClick={toggleFullscreen}
                className="text-white hover:bg-white/20"
              >
                <Maximize className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Loading state */}
        {duration === 0 && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50">
            <div className="text-white">加载中...</div>
          </div>
        )}
      </div>
    </div>
  )
}
