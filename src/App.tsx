import { useState, useEffect } from 'react'
import { Settings } from 'lucide-react'
import { FileItem, LearningProgress, getFileType } from './types'
import { FileTree } from './components/FileTree'
import { VideoPlayer } from './components/VideoPlayer'
import { PDFViewer } from './components/PDFViewer'
import { ImageViewer } from './components/ImageViewer'
import { TextViewer } from './components/TextViewer'
import { SettingsPanel } from './components/SettingsPanel'
import { ResizablePanel } from './components/ResizablePanel'
import { DraggableTitleBar } from './components/DraggableTitleBar'
import { NavigationButtons } from './components/NavigationButtons'
import { Button } from './components/ui/button'
import { databaseService } from './services/database'
import { setupMockAPIs } from './mock/mockElectronAPI'
import './App.css'

function App() {
  const [currentPath, setCurrentPath] = useState<string | null>(null)
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null)
  const [progress, setProgress] = useState<LearningProgress | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [fileList, setFileList] = useState<FileItem[]>([])
  const [currentFileIndex, setCurrentFileIndex] = useState(-1)

  useEffect(() => {
    initializeApp()
  }, [])

  const initializeApp = async () => {
    try {
      // Setup mock APIs for browser development
      setupMockAPIs()

      await databaseService.initialize()

      // Load last opened directory
      const lastPath = await databaseService.getSetting('lastOpenedDirectory')
      if (lastPath) {
        setCurrentPath(lastPath)
      }
    } catch (error) {
      console.error('Failed to initialize app:', error)
    }
  }

  const handlePathChange = (path: string) => {
    setCurrentPath(path)
    setSelectedFile(null)
  }

  const handleFileSelect = (file: FileItem) => {
    setSelectedFile(file)
    loadFileProgress(file)

    // Update file list and current index for navigation
    if (currentPath) {
      loadFileListForNavigation(currentPath, file)
    }
  }

  const loadFileListForNavigation = async (path: string, selectedFile: FileItem) => {
    try {
      const items = await window.electronAPI.readDirectory(path)
      const files = items.filter(item => !item.isDirectory)
      setFileList(files)

      const index = files.findIndex(f => f.path === selectedFile.path)
      setCurrentFileIndex(index)
    } catch (error) {
      console.error('Failed to load file list for navigation:', error)
    }
  }

  const navigateToPrevious = () => {
    if (currentFileIndex > 0) {
      const prevFile = fileList[currentFileIndex - 1]
      setSelectedFile(prevFile)
      setCurrentFileIndex(currentFileIndex - 1)
      loadFileProgress(prevFile)
    }
  }

  const navigateToNext = () => {
    if (currentFileIndex < fileList.length - 1) {
      const nextFile = fileList[currentFileIndex + 1]
      setSelectedFile(nextFile)
      setCurrentFileIndex(currentFileIndex + 1)
      loadFileProgress(nextFile)
    }
  }

  const loadFileProgress = async (file: FileItem) => {
    try {
      const fileProgress = await databaseService.getLearningProgress(file.path)
      setProgress(fileProgress)
    } catch (error) {
      console.error('Failed to load file progress:', error)
      setProgress(null)
    }
  }

  const handleProgressUpdate = (updatedProgress: LearningProgress) => {
    setProgress(updatedProgress)
  }

  const renderFileViewer = () => {
    if (!selectedFile) {
      return (
        <div className="h-full flex items-center justify-center text-muted-foreground">
          <div className="text-center">
            <div className="text-6xl mb-4">📁</div>
            <div className="text-xl mb-2">选择一个文件开始学习</div>
            <div className="text-sm">
              支持视频、PDF、图片和文本文件
            </div>
          </div>
        </div>
      )
    }

    const fileType = getFileType(selectedFile.extension)

    switch (fileType) {
      case 'video':
        return (
          <VideoPlayer
            file={selectedFile}
            onProgressUpdate={handleProgressUpdate}
          />
        )
      case 'pdf':
        return (
          <PDFViewer
            file={selectedFile}
            onProgressUpdate={handleProgressUpdate}
          />
        )
      case 'image':
        return (
          <ImageViewer
            file={selectedFile}
            onProgressUpdate={handleProgressUpdate}
          />
        )
      case 'text':
        return (
          <TextViewer
            file={selectedFile}
            onProgressUpdate={handleProgressUpdate}
          />
        )
      default:
        return (
          <div className="h-full flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <div className="text-4xl mb-4">📄</div>
              <div className="text-lg mb-2">不支持的文件类型</div>
              <div className="text-sm">
                {selectedFile.name}
              </div>
              <div className="text-xs mt-2">
                支持的格式：视频 (mp4, avi, mov 等)、PDF、图片 (jpg, png 等)、文本 (txt, md 等)
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="h-screen flex flex-col bg-background text-foreground">
      {/* Draggable Title Bar */}
      <DraggableTitleBar />

      {/* Main Content */}
      <div className="flex-1 min-h-0">
        <ResizablePanel
          leftPanel={
            <FileTree
              currentPath={currentPath}
              onPathChange={handlePathChange}
              onFileSelect={handleFileSelect}
              selectedFile={selectedFile}
            />
          }
          rightPanel={
            <div className="flex-container-full">
              {/* Fixed Header with File Info and Navigation */}
              <div className="flex-shrink-0 border-b bg-card">
                <div className="h-12 flex items-center justify-between px-4">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    {selectedFile && (
                      <>
                        <span className="font-medium truncate">{selectedFile.name}</span>
                        {progress && (
                          <div className="flex items-center gap-2 ml-4">
                            <div className="text-sm text-muted-foreground">
                              进度: {Math.round(progress.progress)}%
                            </div>
                            {(progress.isCompleted || progress.isMarkedAsLearned) && (
                              <span className="text-green-600 text-sm">✓ 已学习</span>
                            )}
                          </div>
                        )}
                      </>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <NavigationButtons
                      onPrevious={navigateToPrevious}
                      onNext={navigateToNext}
                      hasPrevious={currentFileIndex > 0}
                      hasNext={currentFileIndex < fileList.length - 1}
                    />

                    <div className="w-px h-6 bg-border mx-1" />

                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setShowSettings(true)}
                      className="h-8 w-8"
                    >
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* File Viewer Content */}
              <div className="flex-1 min-h-0">
                {renderFileViewer()}
              </div>
            </div>
          }
        />
      </div>

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        onSettingsChange={() => {
          // Refresh file tree to update progress indicators
          if (selectedFile) {
            loadFileProgress(selectedFile)
          }
        }}
      />
    </div>
  )
}

export default App
