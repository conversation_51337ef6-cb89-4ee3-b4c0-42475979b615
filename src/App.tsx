import { useState, useEffect } from 'react'
import { Settings } from 'lucide-react'
import { FileItem, LearningProgress, getFileType } from './types'
import { FileTree } from './components/FileTree'
import { VideoPlayer } from './components/VideoPlayer'
import { PDFViewer } from './components/PDFViewer'
import { ImageViewer } from './components/ImageViewer'
import { TextViewer } from './components/TextViewer'
import { SettingsPanel } from './components/SettingsPanel'
import { ResizablePanel } from './components/ResizablePanel'

import { NavigationButtons } from './components/NavigationButtons'
import { Button } from './components/ui/button'
import { databaseService } from './services/database'
import { setupMockAPIs } from './mock/mockElectronAPI'
import './App.css'

function App() {
  const [currentPath, setCurrentPath] = useState<string | null>(null)
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null)
  const [progress, setProgress] = useState<LearningProgress | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [fileList, setFileList] = useState<FileItem[]>([])
  const [currentFileIndex, setCurrentFileIndex] = useState(-1)

  useEffect(() => {
    initializeApp()
  }, [])

  const initializeApp = async () => {
    try {
      // Setup mock APIs for browser development
      setupMockAPIs()

      await databaseService.initialize()

      // Load last opened directory
      const lastPath = await databaseService.getSetting('lastOpenedDirectory')
      if (lastPath) {
        setCurrentPath(lastPath)
      }
    } catch (error) {
      console.error('Failed to initialize app:', error)
    }
  }

  const handlePathChange = (path: string) => {
    setCurrentPath(path)
    setSelectedFile(null)
  }

  const handleFileSelect = (file: FileItem) => {
    setSelectedFile(file)
    loadFileProgress(file)

    // Update file list and current index for navigation
    if (currentPath) {
      loadFileListForNavigation(currentPath, file)
    }
  }

  // 系统文件和隐藏文件过滤列表（与FileTree保持一致）
  const HIDDEN_FILES = [
    '.DS_Store',
    'Thumbs.db',
    'desktop.ini',
    '.localized',
    '.fseventsd',
    '.Spotlight-V100',
    '.Trashes',
    '.TemporaryItems',
    '.DocumentRevisions-V100',
    '.VolumeIcon.icns',
    '.com.apple.timemachine.donotpresent',
    '.AppleDouble',
    '.LSOverride',
    '.AppleDB',
    '.AppleDesktop',
    'Network Trash Folder',
    'Temporary Items',
    '.apdisk'
  ]

  const shouldHideFile = (fileName: string): boolean => {
    // 隐藏以点开头的文件（除了一些常见的开发文件）
    if (fileName.startsWith('.')) {
      const allowedDotFiles = ['.gitignore', '.env', '.env.example', '.editorconfig', '.prettierrc', '.eslintrc']
      return !allowedDotFiles.some(allowed => fileName.startsWith(allowed))
    }

    // 隐藏系统文件
    return HIDDEN_FILES.includes(fileName)
  }

  // 获取所有可查看的文件（递归遍历目录）
  const getAllViewableFiles = async (dirPath: string): Promise<FileItem[]> => {
    try {
      const items = await window.electronAPI.readDirectory(dirPath)
      const filteredItems = items.filter(item => !shouldHideFile(item.name))

      let allFiles: FileItem[] = []

      for (const item of filteredItems) {
        if (item.isDirectory) {
          // 递归获取子目录中的文件
          const subFiles = await getAllViewableFiles(item.path)
          allFiles = allFiles.concat(subFiles)
        } else {
          // 只包含支持的文件类型
          const fileType = getFileType(item.extension)
          if (fileType !== 'other') {
            allFiles.push(item)
          }
        }
      }

      return allFiles
    } catch (error) {
      console.error('Failed to get viewable files:', error)
      return []
    }
  }

  const loadFileListForNavigation = async (path: string, selectedFile: FileItem) => {
    try {
      // 获取所有可查看的文件
      const allFiles = await getAllViewableFiles(path)
      // 按文件路径排序，确保导航顺序一致
      const sortedFiles = allFiles.sort((a, b) => a.path.localeCompare(b.path))
      setFileList(sortedFiles)

      const index = sortedFiles.findIndex(f => f.path === selectedFile.path)
      setCurrentFileIndex(index)
    } catch (error) {
      console.error('Failed to load file list for navigation:', error)
    }
  }

  const navigateToPrevious = () => {
    if (currentFileIndex > 0) {
      const prevFile = fileList[currentFileIndex - 1]
      setSelectedFile(prevFile)
      setCurrentFileIndex(currentFileIndex - 1)
      loadFileProgress(prevFile)
    }
  }

  const navigateToNext = () => {
    if (currentFileIndex < fileList.length - 1) {
      const nextFile = fileList[currentFileIndex + 1]
      setSelectedFile(nextFile)
      setCurrentFileIndex(currentFileIndex + 1)
      loadFileProgress(nextFile)
    }
  }

  const loadFileProgress = async (file: FileItem) => {
    try {
      const fileProgress = await databaseService.getLearningProgress(file.path)
      setProgress(fileProgress)
    } catch (error) {
      console.error('Failed to load file progress:', error)
      setProgress(null)
    }
  }

  const handleProgressUpdate = (updatedProgress: LearningProgress) => {
    setProgress(updatedProgress)
    // 触发FileTree刷新进度数据
    setRefreshTrigger(prev => prev + 1)
  }

  const renderFileViewer = () => {
    if (!selectedFile) {
      return (
        <div className="h-full flex items-center justify-center text-muted-foreground">
          <div className="text-center">
            <div className="text-6xl mb-4">📁</div>
            <div className="text-xl mb-2">选择一个文件开始学习</div>
            <div className="text-sm">
              支持视频、PDF、图片和文本文件
            </div>
          </div>
        </div>
      )
    }

    const fileType = getFileType(selectedFile.extension)

    switch (fileType) {
      case 'video':
        return (
          <VideoPlayer
            file={selectedFile}
            onProgressUpdate={handleProgressUpdate}
          />
        )
      case 'pdf':
        return (
          <PDFViewer
            file={selectedFile}
            onProgressUpdate={handleProgressUpdate}
          />
        )
      case 'image':
        return (
          <ImageViewer
            file={selectedFile}
            onProgressUpdate={handleProgressUpdate}
          />
        )
      case 'text':
        return (
          <TextViewer
            file={selectedFile}
            onProgressUpdate={handleProgressUpdate}
          />
        )
      default:
        return (
          <div className="h-full flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <div className="text-4xl mb-4">📄</div>
              <div className="text-lg mb-2">不支持的文件类型</div>
              <div className="text-sm">
                {selectedFile.name}
              </div>
              <div className="text-xs mt-2">
                支持的格式：视频 (mp4, avi, mov 等)、PDF、图片 (jpg, png 等)、文本 (txt, md 等)
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="h-screen flex flex-col bg-background text-foreground">
      {/* Main Content */}
      <div className="flex-1 min-h-0">
        <ResizablePanel
          leftPanel={
            <FileTree
              currentPath={currentPath}
              onPathChange={handlePathChange}
              onFileSelect={handleFileSelect}
              selectedFile={selectedFile}
              refreshTrigger={refreshTrigger}
            />
          }
          rightPanel={
            <div className="flex-container-full">
              {/* Fixed Header with File Info and Navigation */}
              <div className="flex-shrink-0 border-b bg-card">
                <div className="h-12 flex items-center justify-between px-4">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    {selectedFile && (
                      <>
                        <span className="font-medium truncate">{selectedFile.name}</span>
                        {progress && (
                          <div className="flex items-center gap-2 ml-4">
                            <div className="text-sm text-muted-foreground">
                              进度: {Math.round(progress.progress)}%
                            </div>
                            {(progress.isCompleted || progress.isMarkedAsLearned) && (
                              <span className="text-green-600 text-sm">✓ 已学习</span>
                            )}
                          </div>
                        )}
                      </>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <NavigationButtons
                      onPrevious={navigateToPrevious}
                      onNext={navigateToNext}
                      hasPrevious={currentFileIndex > 0}
                      hasNext={currentFileIndex < fileList.length - 1}
                    />

                    <div className="w-px h-6 bg-border mx-1" />

                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setShowSettings(true)}
                      className="h-8 w-8"
                    >
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* File Viewer Content */}
              <div className="flex-1 min-h-0">
                {renderFileViewer()}
              </div>
            </div>
          }
        />
      </div>

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        onSettingsChange={() => {
          // Refresh file tree to update progress indicators
          if (selectedFile) {
            loadFileProgress(selectedFile)
          }
        }}
      />
    </div>
  )
}

export default App
