import { LearningProgress, AppSettings, DirectoryState } from '../types'

// Check if we're in browser environment
const isBrowser = typeof window !== 'undefined' && !window.electronAPI

class DatabaseService {
  private db: any = null
  private isInitialized = false

  async initialize() {
    if (this.isInitialized) return

    // For now, use localStorage as a simple storage solution
    // In a real Electron app, you would use SQLite
    this.isInitialized = true
    await this.createTables()

    // In browser environment, initialize with mock data
    if (isBrowser) {
      await this.initializeMockData()
    }
  }

  private async createTables() {
    // Initialize default settings if they don't exist
    const defaultSettings = [
      { key: 'videoPlaybackSpeed', value: '1' },
      { key: 'autoMarkAsLearned', value: 'true' },
      { key: 'theme', value: 'light' },
      { key: 'lastOpenedDirectory', value: '' },
    ]

    for (const setting of defaultSettings) {
      if (!localStorage.getItem(`setting_${setting.key}`)) {
        localStorage.setItem(`setting_${setting.key}`, setting.value)
      }
    }
  }

  private async initializeMockData() {
    // Only initialize mock data if no data exists
    if (!localStorage.getItem('learning_progress_initialized')) {
      const { mockProgress, mockSettings } = await import('../mock/mockData')

      // Initialize mock learning progress
      mockProgress.forEach((progress, index) => {
        localStorage.setItem(`progress_${index}`, JSON.stringify(progress))
      })

      // Initialize mock settings
      Object.entries(mockSettings).forEach(([key, value]) => {
        localStorage.setItem(`setting_${key}`, value)
      })

      localStorage.setItem('learning_progress_initialized', 'true')
      console.log('🎭 Mock data initialized')
    }
  }

  // Learning Progress methods
  async getLearningProgress(filePath: string): Promise<LearningProgress | null> {
    const data = localStorage.getItem(`progress_${filePath}`)
    return data ? JSON.parse(data) : null
  }

  async saveLearningProgress(progress: LearningProgress): Promise<void> {
    progress.lastAccessed = new Date()
    localStorage.setItem(`progress_${progress.filePath}`, JSON.stringify(progress))
  }

  async getAllLearningProgress(): Promise<LearningProgress[]> {
    const progressItems: LearningProgress[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key?.startsWith('progress_')) {
        const data = localStorage.getItem(key)
        if (data) {
          progressItems.push(JSON.parse(data))
        }
      }
    }

    return progressItems.sort((a, b) =>
      new Date(b.lastAccessed).getTime() - new Date(a.lastAccessed).getTime()
    )
  }

  async deleteLearningProgress(filePath: string): Promise<void> {
    localStorage.removeItem(`progress_${filePath}`)
  }

  // Settings methods
  async getSetting(key: string): Promise<string | null> {
    return localStorage.getItem(`setting_${key}`)
  }

  async setSetting(key: string, value: string): Promise<void> {
    localStorage.setItem(`setting_${key}`, value)
  }

  async getAllSettings(): Promise<AppSettings[]> {
    const settings: AppSettings[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const storageKey = localStorage.key(i)
      if (storageKey?.startsWith('setting_')) {
        const key = storageKey.replace('setting_', '')
        const value = localStorage.getItem(storageKey)
        if (value !== null) {
          settings.push({ key, value })
        }
      }
    }

    return settings
  }

  // Directory State methods
  async getDirectoryState(path: string): Promise<DirectoryState | null> {
    const data = localStorage.getItem(`directory_${path}`)
    return data ? JSON.parse(data) : null
  }

  async saveDirectoryState(state: DirectoryState): Promise<void> {
    state.lastAccessed = new Date()
    localStorage.setItem(`directory_${state.path}`, JSON.stringify(state))
  }

  async getAllDirectoryStates(): Promise<DirectoryState[]> {
    const states: DirectoryState[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key?.startsWith('directory_')) {
        const data = localStorage.getItem(key)
        if (data) {
          states.push(JSON.parse(data))
        }
      }
    }

    return states.sort((a, b) =>
      new Date(b.lastAccessed).getTime() - new Date(a.lastAccessed).getTime()
    )
  }

  async deleteDirectoryState(path: string): Promise<void> {
    localStorage.removeItem(`directory_${path}`)
  }

  // Utility methods
  async getRecentFiles(limit: number = 10): Promise<LearningProgress[]> {
    const allProgress = await this.getAllLearningProgress()
    return allProgress.slice(0, limit)
  }

  async getCompletedFiles(): Promise<LearningProgress[]> {
    const allProgress = await this.getAllLearningProgress()
    return allProgress.filter(p => p.isCompleted || p.isMarkedAsLearned)
  }

  async getInProgressFiles(): Promise<LearningProgress[]> {
    const allProgress = await this.getAllLearningProgress()
    return allProgress.filter(p => !p.isCompleted && !p.isMarkedAsLearned && p.progress > 0)
  }
}

export const databaseService = new DatabaseService()
