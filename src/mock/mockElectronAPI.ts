import { FileItem } from '../types'
import { mockFiles, mockSubFiles } from './mockData'

// Mock Electron API for browser development
export const mockElectronAPI = {
  // File system operations
  readDirectory: async (path: string): Promise<FileItem[]> => {
    console.log('Mock readDirectory:', path)
    
    // Simulate delay
    await new Promise(resolve => setTimeout(resolve, 300))
    
    if (path === '/Users/<USER>/Documents/学习资料' || !path) {
      return mockFiles
    }
    
    return mockSubFiles[path] || []
  },

  selectDirectory: async (): Promise<string | null> => {
    console.log('Mock selectDirectory')
    
    // Simulate directory selection dialog
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return '/Users/<USER>/Documents/学习资料'
  },

  openExternal: async (url: string): Promise<void> => {
    console.log('Mock openExternal:', url)
    
    // In browser, we can actually open the URL
    if (url.startsWith('http')) {
      window.open(url, '_blank')
    } else {
      alert(`模拟打开外部程序: ${url}`)
    }
  },

  showItemInFolder: async (path: string): Promise<void> => {
    console.log('Mock showItemInFolder:', path)
    alert(`模拟在文件夹中显示: ${path}`)
  },

  // Window operations
  minimizeWindow: async (): Promise<void> => {
    console.log('Mock minimizeWindow')
    alert('模拟最小化窗口')
  },

  maximizeWindow: async (): Promise<void> => {
    console.log('Mock maximizeWindow')
    alert('模拟最大化窗口')
  },

  closeWindow: async (): Promise<void> => {
    console.log('Mock closeWindow')
    if (confirm('确定要关闭窗口吗？')) {
      window.close()
    }
  },

  // App operations
  getAppVersion: async (): Promise<string> => {
    return '1.0.0-mock'
  },

  // Platform info
  getPlatform: async (): Promise<string> => {
    return navigator.platform.toLowerCase().includes('mac') ? 'darwin' : 
           navigator.platform.toLowerCase().includes('win') ? 'win32' : 'linux'
  }
}

// Mock database service for browser
export const mockDatabaseService = {
  initialize: async (): Promise<void> => {
    console.log('Mock database initialized')
  },

  // Learning progress
  getLearningProgress: async (filePath: string) => {
    const { mockProgress } = await import('./mockData')
    return mockProgress.find(p => p.filePath === filePath) || null
  },

  getAllLearningProgress: async () => {
    const { mockProgress } = await import('./mockData')
    return mockProgress
  },

  saveLearningProgress: async (progress: any) => {
    console.log('Mock save progress:', progress)
    return progress
  },

  // Settings
  getSetting: async (key: string) => {
    const { mockSettings } = await import('./mockData')
    return (mockSettings as any)[key] || null
  },

  setSetting: async (key: string, value: string) => {
    console.log('Mock set setting:', key, value)
  },

  // Directory state
  saveDirectoryState: async (state: any) => {
    console.log('Mock save directory state:', state)
  },

  getDirectoryStates: async () => {
    return []
  }
}

// Setup mock APIs in browser environment
export const setupMockAPIs = () => {
  if (typeof window !== 'undefined' && !window.electronAPI) {
    // Mock window.electronAPI
    (window as any).electronAPI = mockElectronAPI
    
    console.log('🎭 Mock Electron APIs initialized for browser development')
  }
}
