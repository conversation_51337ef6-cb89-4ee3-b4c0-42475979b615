// Sample content for different file types in browser development

export const sampleTextContent = `# TypeScript 学习文档

## 什么是 TypeScript？

TypeScript 是 JavaScript 的一个超集，它添加了静态类型定义。TypeScript 代码会被编译成纯 JavaScript 代码，可以在任何支持 JavaScript 的环境中运行。

## 主要特性

### 1. 静态类型检查
TypeScript 在编译时进行类型检查，可以在开发阶段发现潜在的错误。

\`\`\`typescript
// 基本类型
let name: string = "张三";
let age: number = 25;
let isStudent: boolean = true;

// 数组类型
let numbers: number[] = [1, 2, 3, 4, 5];
let names: Array<string> = ["张三", "李四", "王五"];
\`\`\`

### 2. 接口 (Interfaces)
接口用于定义对象的结构：

\`\`\`typescript
interface User {
  id: number;
  name: string;
  email: string;
  age?: number; // 可选属性
}

const user: User = {
  id: 1,
  name: "张三",
  email: "z<PERSON><PERSON>@example.com"
};
\`\`\`

### 3. 类 (Classes)
TypeScript 支持面向对象编程：

\`\`\`typescript
class Animal {
  private name: string;
  
  constructor(name: string) {
    this.name = name;
  }
  
  public speak(): void {
    console.log(\`\${this.name} makes a sound\`);
  }
}

class Dog extends Animal {
  constructor(name: string) {
    super(name);
  }
  
  public speak(): void {
    console.log(\`\${this.name} barks\`);
  }
}
\`\`\`

### 4. 泛型 (Generics)
泛型提供了创建可重用组件的方式：

\`\`\`typescript
function identity<T>(arg: T): T {
  return arg;
}

let output1 = identity<string>("hello");
let output2 = identity<number>(42);
\`\`\`

## 配置文件

TypeScript 项目通常包含一个 \`tsconfig.json\` 配置文件：

\`\`\`json
{
  "compilerOptions": {
    "target": "es2018",
    "module": "commonjs",
    "lib": ["es2018"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
\`\`\`

## 总结

TypeScript 为 JavaScript 开发带来了以下优势：

1. **类型安全**: 在编译时捕获错误
2. **更好的 IDE 支持**: 自动完成、重构等
3. **代码可读性**: 类型注解使代码更易理解
4. **渐进式采用**: 可以逐步将 JavaScript 项目迁移到 TypeScript

学习 TypeScript 是现代前端开发的重要技能，它能显著提高代码质量和开发效率。`

export const sampleImageUrl = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vZGUuanMg5p625p6E5Zu+PC90ZXh0Pgo8L3N2Zz4K'

export const sampleVideoUrl = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'

export const samplePdfUrl = 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf'
