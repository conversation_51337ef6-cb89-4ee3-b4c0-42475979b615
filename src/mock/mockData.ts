import { FileItem, LearningProgress } from '../types'

// Mock file data - Extended list to test scrolling
export const mockFiles: FileItem[] = [
  {
    name: 'JavaScript基础教程.pdf',
    path: '/Users/<USER>/Documents/学习资料/JavaScript基础教程.pdf',
    size: 2048576,
    modified: Date.now() - 86400000,
    isDirectory: false,
    extension: '.pdf'
  },
  {
    name: 'React入门视频.mp4',
    path: '/Users/<USER>/Documents/学习资料/React入门视频.mp4',
    size: 104857600,
    modified: Date.now() - 172800000,
    isDirectory: false,
    extension: '.mp4'
  },
  {
    name: 'TypeScript文档.md',
    path: '/Users/<USER>/Documents/学习资料/TypeScript文档.md',
    size: 51200,
    modified: Date.now() - 259200000,
    isDirectory: false,
    extension: '.md'
  },
  {
    name: 'Node.js架构图.png',
    path: '/Users/<USER>/Documents/学习资料/Node.js架构图.png',
    size: 1024000,
    modified: Date.now() - 345600000,
    isDirectory: false,
    extension: '.png'
  },
  {
    name: '前端开发',
    path: '/Users/<USER>/Documents/学习资料/前端开发',
    size: 0,
    modified: Date.now() - 432000000,
    isDirectory: true,
    extension: ''
  },
  {
    name: '后端开发',
    path: '/Users/<USER>/Documents/学习资料/后端开发',
    size: 0,
    modified: Date.now() - 518400000,
    isDirectory: true,
    extension: ''
  },
  // Additional files to test scrolling
  {
    name: 'Vue.js完整教程.pdf',
    path: '/Users/<USER>/Documents/学习资料/Vue.js完整教程.pdf',
    size: 3145728,
    modified: Date.now() - 604800000,
    isDirectory: false,
    extension: '.pdf'
  },
  {
    name: 'Angular开发指南.pdf',
    path: '/Users/<USER>/Documents/学习资料/Angular开发指南.pdf',
    size: 2621440,
    modified: Date.now() - 691200000,
    isDirectory: false,
    extension: '.pdf'
  },
  {
    name: 'Python数据分析.mp4',
    path: '/Users/<USER>/Documents/学习资料/Python数据分析.mp4',
    size: 157286400,
    modified: Date.now() - 777600000,
    isDirectory: false,
    extension: '.mp4'
  },
  {
    name: 'Docker容器化部署.mp4',
    path: '/Users/<USER>/Documents/学习资料/Docker容器化部署.mp4',
    size: 209715200,
    modified: Date.now() - 864000000,
    isDirectory: false,
    extension: '.mp4'
  },
  {
    name: 'Kubernetes集群管理.pdf',
    path: '/Users/<USER>/Documents/学习资料/Kubernetes集群管理.pdf',
    size: 4194304,
    modified: Date.now() - 950400000,
    isDirectory: false,
    extension: '.pdf'
  },
  {
    name: 'Git版本控制.md',
    path: '/Users/<USER>/Documents/学习资料/Git版本控制.md',
    size: 76800,
    modified: Date.now() - 1036800000,
    isDirectory: false,
    extension: '.md'
  },
  {
    name: 'Linux系统管理.md',
    path: '/Users/<USER>/Documents/学习资料/Linux系统管理.md',
    size: 102400,
    modified: Date.now() - 1123200000,
    isDirectory: false,
    extension: '.md'
  },
  {
    name: 'MySQL数据库设计.pdf',
    path: '/Users/<USER>/Documents/学习资料/MySQL数据库设计.pdf',
    size: 1572864,
    modified: Date.now() - 1209600000,
    isDirectory: false,
    extension: '.pdf'
  },
  {
    name: 'Redis缓存优化.md',
    path: '/Users/<USER>/Documents/学习资料/Redis缓存优化.md',
    size: 61440,
    modified: Date.now() - 1296000000,
    isDirectory: false,
    extension: '.md'
  },
  {
    name: 'Nginx配置详解.md',
    path: '/Users/<USER>/Documents/学习资料/Nginx配置详解.md',
    size: 40960,
    modified: Date.now() - 1382400000,
    isDirectory: false,
    extension: '.md'
  },
  {
    name: 'AWS云服务架构.png',
    path: '/Users/<USER>/Documents/学习资料/AWS云服务架构.png',
    size: 2048000,
    modified: Date.now() - 1468800000,
    isDirectory: false,
    extension: '.png'
  },
  {
    name: '微服务架构设计.png',
    path: '/Users/<USER>/Documents/学习资料/微服务架构设计.png',
    size: 1536000,
    modified: Date.now() - 1555200000,
    isDirectory: false,
    extension: '.png'
  },
  {
    name: '移动开发',
    path: '/Users/<USER>/Documents/学习资料/移动开发',
    size: 0,
    modified: Date.now() - 1641600000,
    isDirectory: true,
    extension: ''
  },
  {
    name: '数据科学',
    path: '/Users/<USER>/Documents/学习资料/数据科学',
    size: 0,
    modified: Date.now() - 1728000000,
    isDirectory: true,
    extension: ''
  }
]

// Mock subdirectory files
export const mockSubFiles: { [key: string]: FileItem[] } = {
  '/Users/<USER>/Documents/学习资料/前端开发': [
    {
      name: 'HTML5新特性.pdf',
      path: '/Users/<USER>/Documents/学习资料/前端开发/HTML5新特性.pdf',
      size: 1536000,
      modified: Date.now() - 604800000,
      isDirectory: false,
      extension: '.pdf'
    },
    {
      name: 'CSS3动画教程.mp4',
      path: '/Users/<USER>/Documents/学习资料/前端开发/CSS3动画教程.mp4',
      size: 83886080,
      modified: Date.now() - 691200000,
      isDirectory: false,
      extension: '.mp4'
    },
    {
      name: 'Vue.js实战项目',
      path: '/Users/<USER>/Documents/学习资料/前端开发/Vue.js实战项目',
      size: 0,
      modified: Date.now() - 777600000,
      isDirectory: true,
      extension: ''
    }
  ],
  '/Users/<USER>/Documents/学习资料/后端开发': [
    {
      name: 'Express框架教程.pdf',
      path: '/Users/<USER>/Documents/学习资料/后端开发/Express框架教程.pdf',
      size: 2097152,
      modified: Date.now() - 864000000,
      isDirectory: false,
      extension: '.pdf'
    },
    {
      name: 'MongoDB数据库.mp4',
      path: '/Users/<USER>/Documents/学习资料/后端开发/MongoDB数据库.mp4',
      size: 125829120,
      modified: Date.now() - 950400000,
      isDirectory: false,
      extension: '.mp4'
    }
  ],
  '/Users/<USER>/Documents/学习资料/前端开发/Vue.js实战项目': [
    {
      name: '项目搭建.md',
      path: '/Users/<USER>/Documents/学习资料/前端开发/Vue.js实战项目/项目搭建.md',
      size: 25600,
      modified: Date.now() - 1036800000,
      isDirectory: false,
      extension: '.md'
    },
    {
      name: '组件开发.md',
      path: '/Users/<USER>/Documents/学习资料/前端开发/Vue.js实战项目/组件开发.md',
      size: 38400,
      modified: Date.now() - 1123200000,
      isDirectory: false,
      extension: '.md'
    }
  ]
}

// Mock learning progress data
export const mockProgress: LearningProgress[] = [
  {
    filePath: '/Users/<USER>/Documents/学习资料/JavaScript基础教程.pdf',
    fileType: 'pdf',
    progress: 75,
    currentPosition: 15,
    totalDuration: 20,
    isCompleted: false,
    isMarkedAsLearned: false,
    lastAccessed: new Date(Date.now() - 3600000),
    notes: '学习了变量和函数部分'
  },
  {
    filePath: '/Users/<USER>/Documents/学习资料/React入门视频.mp4',
    fileType: 'video',
    progress: 100,
    currentPosition: 3600,
    totalDuration: 3600,
    isCompleted: true,
    isMarkedAsLearned: true,
    lastAccessed: new Date(Date.now() - 7200000),
    notes: '已完成学习，掌握了基本概念'
  },
  {
    filePath: '/Users/<USER>/Documents/学习资料/TypeScript文档.md',
    fileType: 'text',
    progress: 50,
    currentPosition: 1,
    totalDuration: 1,
    isCompleted: false,
    isMarkedAsLearned: false,
    lastAccessed: new Date(Date.now() - 10800000),
    notes: '正在学习类型系统'
  },
  {
    filePath: '/Users/<USER>/Documents/学习资料/Node.js架构图.png',
    fileType: 'image',
    progress: 100,
    currentPosition: 1,
    totalDuration: 1,
    isCompleted: true,
    isMarkedAsLearned: true,
    lastAccessed: new Date(Date.now() - 14400000),
    notes: '已查看'
  },
  {
    filePath: '/Users/<USER>/Documents/学习资料/前端开发/HTML5新特性.pdf',
    fileType: 'pdf',
    progress: 30,
    currentPosition: 3,
    totalDuration: 10,
    isCompleted: false,
    isMarkedAsLearned: false,
    lastAccessed: new Date(Date.now() - 18000000),
    notes: '学习了语义化标签'
  }
]

// Mock settings
export const mockSettings = {
  lastOpenedDirectory: '/Users/<USER>/Documents/学习资料',
  videoPlaybackSpeed: '1',
  theme: 'light'
}
