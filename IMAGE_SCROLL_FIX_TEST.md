# 图片查看器滚动修复测试指南

## 修复的问题
原始问题：图片查看器强制居中显示，当图片高度超出容器时无法正常从上到下滚动。

## 修复方案详解

### 核心逻辑
```typescript
justifyContent: (scale === 'auto' && actualScale < 1) ? 'center' : 'flex-start'
```

### 布局行为说明

#### 1. 小图片（被缩小显示）
- **条件**: `scale === 'auto' && actualScale < 1`
- **行为**: `justify-content: center` - 垂直居中显示
- **原因**: 图片小于容器，居中显示更美观

#### 2. 大图片（原始大小或放大）
- **条件**: `scale !== 'auto' || actualScale >= 1`
- **行为**: `justify-content: flex-start` - 从顶部开始显示
- **原因**: 图片大于或等于容器，需要支持滚动查看

#### 3. 手动缩放
- **条件**: `scale !== 'auto'`（用户手动设置缩放）
- **行为**: `justify-content: flex-start` - 从顶部开始显示
- **原因**: 用户可能放大图片，需要支持滚动

## 测试场景

### 场景1：小图片测试
1. 选择一个小于显示区域的图片
2. 确认图片在容器中垂直居中显示
3. 确认没有滚动条出现

### 场景2：大图片测试
1. 选择一个大于显示区域的图片
2. 确认图片从顶部开始显示
3. 确认可以向下滚动查看图片底部
4. 确认滚动时内容不被头部遮挡

### 场景3：缩放测试
1. 选择任意图片
2. 使用放大功能（点击放大按钮）
3. 确认放大后图片从顶部开始显示
4. 确认可以滚动查看放大后的图片全部内容

### 场景4：自适应模式测试
1. 选择大图片
2. 点击"自适应"按钮
3. 如果图片被缩小以适应容器，确认居中显示
4. 如果图片保持原始大小，确认从顶部开始显示

## 技术实现细节

### CSS 布局结构
```css
.scrollable-container {
  /* 外层容器：支持滚动 */
  overflow-y: auto;
  padding-top: 8px; /* 防止被头部遮挡 */
}

.image-container {
  /* 内层容器：智能布局 */
  min-height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: dynamic; /* 根据图片大小动态调整 */
}
```

### 关键变量
- `scale`: 当前缩放模式（'auto' 或具体数值）
- `actualScale`: 自适应模式下的实际缩放比例
- `actualScale < 1`: 图片被缩小显示
- `actualScale >= 1`: 图片以原始大小或更大显示

## 预期效果
✅ 小图片优雅居中显示
✅ 大图片支持完整滚动查看
✅ 放大图片支持滚动查看
✅ 内容不被固定头部遮挡
✅ 保持良好的用户体验
