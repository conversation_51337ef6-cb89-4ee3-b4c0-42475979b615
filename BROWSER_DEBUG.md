# 浏览器调试指南

## 🌐 启动浏览器版本

您现在可以在浏览器中调试 Local Browser 应用了！

### 启动命令
```bash
pnpm run dev:vite
```

### 访问地址
- **本地地址**: http://localhost:5175/
- **网络地址**: 使用 `--host` 参数可以在局域网中访问

## 🎭 Mock 数据说明

为了在浏览器中进行调试，我们提供了完整的 mock 数据：

### 1. 文件系统 Mock
- **模拟目录**: `/Users/<USER>/Documents/学习资料`
- **文件类型**: PDF、视频、图片、文本、目录
- **子目录**: 前端开发、后端开发等

### 2. 学习进度 Mock
- 自动加载预设的学习进度数据
- 包含不同完成状态的文件
- 支持进度保存和读取

### 3. 示例内容
- **文本文件**: TypeScript 学习文档（Markdown 格式）
- **图片文件**: SVG 示例图片
- **视频文件**: Big Buck Bunny 示例视频
- **PDF文件**: Mozilla PDF.js 示例文档

## 🔧 调试功能

### 可用功能
✅ 文件目录树浏览  
✅ 文件选择和导航  
✅ 学习进度显示  
✅ 文本文件查看（支持搜索、字体调整）  
✅ 图片查看（支持缩放、旋转）  
✅ 视频播放（支持控制、进度保存）  
✅ PDF查看（支持翻页、缩放）  
✅ 设置面板  
✅ 响应式布局  

### 限制功能
❌ 实际文件系统访问  
❌ 本地文件选择对话框  
❌ 系统集成功能  

## 🎯 调试建议

### 1. 开发者工具
- 打开浏览器开发者工具 (F12)
- 查看 Console 面板了解 mock API 调用
- 使用 Network 面板监控资源加载

### 2. 响应式测试
- 使用开发者工具的设备模拟器
- 测试不同屏幕尺寸下的布局

### 3. 功能测试
- 点击"选择目录"按钮（会自动选择 mock 目录）
- 浏览文件树，展开/折叠目录
- 选择不同类型的文件查看内容
- 测试导航按钮（上一个/下一个）
- 调整各种设置选项

### 4. 状态管理
- 学习进度会保存在 localStorage 中
- 刷新页面后状态会保持
- 可以在开发者工具中查看 localStorage

## 🐛 常见问题

### Q: 视频无法播放？
A: 确保网络连接正常，示例视频来自 Google 的公共存储

### Q: PDF 无法显示？
A: 检查浏览器是否支持 PDF.js，或者网络是否能访问示例 PDF

### Q: 文件树为空？
A: 检查控制台是否有错误，确保 mock 数据正确加载

### Q: 样式显示异常？
A: 确保 Tailwind CSS 正确加载，检查控制台错误

## 📝 开发提示

### 修改 Mock 数据
- 编辑 `src/mock/mockData.ts` 添加更多文件
- 修改 `src/mock/sampleContent.ts` 更改示例内容
- 调整 `src/mock/mockElectronAPI.ts` 模拟不同的 API 行为

### 添加新功能
- 在浏览器中快速测试 UI 变更
- 使用热重载功能实时查看效果
- 完成后在 Electron 环境中验证

### 性能优化
- 使用浏览器性能工具分析
- 测试大量文件时的渲染性能
- 优化组件重渲染

## 🚀 部署到生产

当您完成调试后，可以：

1. **构建 Electron 应用**:
   ```bash
   pnpm run build:electron
   ```

2. **构建 Web 版本**:
   ```bash
   pnpm run build
   ```

3. **预览构建结果**:
   ```bash
   pnpm run preview
   ```

祝您调试愉快！🎉
