{"name": "socks-proxy-agent", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "version": "7.0.0", "main": "dist/index.js", "author": {"email": "<EMAIL>", "name": "<PERSON>", "url": "http://n8.io/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "richardka<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-socks-proxy-agent.git"}, "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "dependencies": {"agent-base": "^6.0.2", "debug": "^4.3.3", "socks": "^2.6.2"}, "devDependencies": {"@commitlint/cli": "latest", "@commitlint/config-conventional": "latest", "@types/debug": "latest", "@types/node": "latest", "cacheable-lookup": "latest", "conventional-github-releaser": "latest", "dns2": "latest", "finepack": "latest", "git-authors-cli": "latest", "mocha": "9", "nano-staged": "latest", "npm-check-updates": "latest", "prettier-standard": "latest", "raw-body": "latest", "rimraf": "latest", "simple-git-hooks": "latest", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "standard": "latest", "standard-markdown": "latest", "standard-version": "latest", "ts-standard": "latest", "typescript": "latest"}, "engines": {"node": ">= 10"}, "files": ["dist"], "scripts": {"build": "tsc", "clean": "rimraf node_modules", "contributors": "(git-authors-cli && finepack && git add package.json && git commit -m 'build: contributors' --no-verify) || true", "lint": "ts-standard", "postrelease": "npm run release:tags && npm run release:github && (ci-publish || npm publish --access=public)", "prebuild": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run build", "prerelease": "npm run update:check && npm run contributors", "release": "standard-version -a", "release:github": "conventional-github-releaser -p angular", "release:tags": "git push --follow-tags origin HEAD:master", "test": "mocha --reporter spec", "update": "ncu -u", "update:check": "ncu -- --error-level 2"}, "license": "MIT", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "nano-staged": {"*.js": ["prettier-standard"], "*.md": ["standard-markdown"], "package.json": ["finepack"]}, "simple-git-hooks": {"commit-msg": "npx commitlint --edit", "pre-commit": "npx nano-staged"}, "typings": "dist/index.d.ts"}