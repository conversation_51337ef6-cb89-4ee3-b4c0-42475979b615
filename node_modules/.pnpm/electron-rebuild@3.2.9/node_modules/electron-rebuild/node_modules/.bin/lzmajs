#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/code/demo/local-browser/node_modules/.pnpm/lzma-native@8.0.6/node_modules/lzma-native/bin/node_modules:/Users/<USER>/code/demo/local-browser/node_modules/.pnpm/lzma-native@8.0.6/node_modules/lzma-native/node_modules:/Users/<USER>/code/demo/local-browser/node_modules/.pnpm/lzma-native@8.0.6/node_modules:/Users/<USER>/code/demo/local-browser/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/code/demo/local-browser/node_modules/.pnpm/lzma-native@8.0.6/node_modules/lzma-native/bin/node_modules:/Users/<USER>/code/demo/local-browser/node_modules/.pnpm/lzma-native@8.0.6/node_modules/lzma-native/node_modules:/Users/<USER>/code/demo/local-browser/node_modules/.pnpm/lzma-native@8.0.6/node_modules:/Users/<USER>/code/demo/local-browser/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../lzma-native@8.0.6/node_modules/lzma-native/bin/lzmajs" "$@"
else
  exec node  "$basedir/../../../../../lzma-native@8.0.6/node_modules/lzma-native/bin/lzmajs" "$@"
fi
