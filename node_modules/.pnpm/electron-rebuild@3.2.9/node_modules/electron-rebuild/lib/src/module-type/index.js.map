{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/module-type/index.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAA0B;AAC1B,gDAAwB;AAExB,0CAAsC;AACtC,4DAAuD;AAKvD,MAAa,YAAY;IAOvB,YAAY,SAAqB,EAAE,UAAkB;QACnD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,IAAI,kBAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;IAC9E,CAAC;IAED,IAAI,UAAU;QACZ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,MAAM,SAAS,GAAG,cAAI,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YAC/D,IAAI,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBAC7B,IAAI,CAAC,WAAW,GAAG,GAAG,SAAS,IAAI,QAAQ,EAAE,CAAC;aAC/C;YAED,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;SAC7B;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,GAAW,EAAE,YAA8B;QAC3E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAChD,OAAO,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,GAAW;QAChC,IAAI,CAAC,WAAW,KAAhB,IAAI,CAAC,WAAW,GAAK,MAAM,IAAA,mCAAe,EAAC,IAAI,CAAC,UAAU,CAAC,EAAC;QAE5D,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,wBAAwB;QAC5B,MAAM,MAAM,GAAG,CAAC,MAAM,IAAI,CAAC,2BAA2B,CACpD,QAAQ,EACR,EAAE,CACH,CAA6B,CAAC;QAE/B,OAAO,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,aAAa,CAAC;IAC/B,CAAC;CACF;AA9CD,oCA8CC;AAEM,KAAK,UAAU,YAAY,CAAC,QAAgB,EAAE,MAAc;IACjE,IAAI,UAAU,GAAG,QAAQ,CAAC;IAC1B,IAAI,QAA4B,CAAC;IAEjC,OAAO,QAAQ,KAAK,UAAU,EAAE;QAC9B,QAAQ,GAAG,UAAU,CAAC;QACtB,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACjD,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;YAClC,OAAO,SAAS,CAAC;SAClB;QACD,UAAU,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;KAC3C;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAdD,oCAcC"}