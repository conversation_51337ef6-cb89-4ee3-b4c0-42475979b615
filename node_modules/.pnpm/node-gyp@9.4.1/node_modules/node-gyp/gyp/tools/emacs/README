How to install gyp-mode for emacs:

Add the following to your ~/.emacs (replace ... with the path to your gyp
checkout).

(setq load-path (cons ".../tools/emacs" load-path))
(require 'gyp)

Restart emacs (or eval-region the added lines) and you should be all set.

Please note that ert is required for running the tests, which is included in
Emacs 24, or available separately from https://github.com/ohler/ert
