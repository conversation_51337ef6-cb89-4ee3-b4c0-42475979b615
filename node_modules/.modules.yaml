hoistPattern:
  - '*'
hoistedDependencies:
  7zip-bin@5.2.0:
    7zip-bin: private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.28.2':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.28.2':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@develar/schema-utils@2.6.5':
    '@develar/schema-utils': private
  '@electron/asar@3.4.1':
    '@electron/asar': private
  '@electron/get@2.0.3':
    '@electron/get': private
  '@electron/notarize@2.2.1':
    '@electron/notarize': private
  '@electron/osx-sign@1.0.5':
    '@electron/osx-sign': private
  '@electron/universal@1.5.1':
    '@electron/universal': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@floating-ui/core@1.7.3':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.3':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@gar/promisify@1.1.3':
    '@gar/promisify': private
  '@hapi/hoek@9.3.0':
    '@hapi/hoek': private
  '@hapi/topo@5.1.0':
    '@hapi/topo': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@malept/cross-spawn-promise@1.1.1':
    '@malept/cross-spawn-promise': private
  '@malept/cross-spawn-promise@2.0.0':
    '@malept/cross-spawn-promise': private
  '@malept/flatpak-bundler@0.4.0':
    '@malept/flatpak-bundler': private
  '@mapbox/node-pre-gyp@1.0.11(encoding@0.1.13)':
    '@mapbox/node-pre-gyp': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@npmcli/fs@1.1.1':
    '@npmcli/fs': private
  '@npmcli/move-file@1.1.2':
    '@npmcli/move-file': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.15(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.9(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-slot@1.2.3(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-slot': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@rolldown/pluginutils@1.0.0-beta.27':
    '@rolldown/pluginutils': private
  '@rollup/rollup-darwin-arm64@4.46.2':
    '@rollup/rollup-darwin-arm64': private
  '@sideway/address@4.1.5':
    '@sideway/address': private
  '@sideway/formula@3.0.1':
    '@sideway/formula': private
  '@sideway/pinpoint@2.0.0':
    '@sideway/pinpoint': private
  '@sindresorhus/is@4.6.0':
    '@sindresorhus/is': private
  '@szmarczak/http-timer@4.0.6':
    '@szmarczak/http-timer': private
  '@tootallnate/once@1.1.2':
    '@tootallnate/once': private
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.28.0':
    '@types/babel__traverse': private
  '@types/cacheable-request@6.0.3':
    '@types/cacheable-request': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/fs-extra@9.0.13':
    '@types/fs-extra': private
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/keyv@3.1.4':
    '@types/keyv': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/plist@3.0.5':
    '@types/plist': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/responselike@1.0.3':
    '@types/responselike': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@types/verror@1.10.11':
    '@types/verror': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@6.21.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@6.21.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.9.2)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@6.21.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@xmldom/xmldom@0.8.10':
    '@xmldom/xmldom': private
  abbrev@1.1.1:
    abbrev: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  app-builder-bin@4.0.0:
    app-builder-bin: private
  app-builder-lib@24.13.3(dmg-builder@24.13.3)(electron-builder-squirrel-windows@24.13.3):
    app-builder-lib: private
  aproba@2.1.0:
    aproba: private
  archiver-utils@2.1.0:
    archiver-utils: private
  archiver@5.3.2:
    archiver: private
  are-we-there-yet@3.0.1:
    are-we-there-yet: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  array-union@2.1.0:
    array-union: private
  assert-plus@1.0.0:
    assert-plus: private
  astral-regex@2.0.0:
    astral-regex: private
  async-exit-hook@2.0.1:
    async-exit-hook: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  axios@1.11.0:
    axios: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bindings@1.5.0:
    bindings: private
  bl@4.1.0:
    bl: private
  bluebird-lst@1.0.9:
    bluebird-lst: private
  bluebird@3.7.2:
    bluebird: private
  boolean@3.2.0:
    boolean: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-equal@1.0.1:
    buffer-equal: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@5.7.1:
    buffer: private
  builder-util-runtime@9.2.4:
    builder-util-runtime: private
  builder-util@24.13.1:
    builder-util: private
  cacache@15.3.0:
    cacache: private
  cacache@16.1.3:
    cacache: private
  cacheable-lookup@5.0.4:
    cacheable-lookup: private
  cacheable-request@7.0.4:
    cacheable-request: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001731:
    caniuse-lite: private
  canvas@2.11.2(encoding@0.1.13):
    canvas: private
  chalk@4.1.2:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  chownr@1.1.4:
    chownr: private
  chownr@2.0.0:
    chownr: private
  chromium-pickle-js@0.2.0:
    chromium-pickle-js: private
  ci-info@3.9.0:
    ci-info: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-truncate@2.1.0:
    cli-truncate: private
  cliui@8.0.1:
    cliui: private
  clone-response@1.0.3:
    clone-response: private
  clone@1.0.4:
    clone: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-support@1.1.3:
    color-support: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@4.1.1:
    commander: private
  compare-version@0.1.2:
    compare-version: private
  compress-commons@4.1.2:
    compress-commons: private
  concat-map@0.0.1:
    concat-map: private
  config-file-ts@0.2.6:
    config-file-ts: private
  console-control-strings@1.1.0:
    console-control-strings: private
  convert-source-map@2.0.0:
    convert-source-map: private
  core-util-is@1.0.2:
    core-util-is: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@4.0.3:
    crc32-stream: private
  crc@3.8.0:
    crc: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  date-fns@2.30.0:
    date-fns: private
  debug@4.4.1:
    debug: private
  decompress-response@6.0.0:
    decompress-response: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  defaults@1.0.4:
    defaults: private
  defer-to-connect@2.0.1:
    defer-to-connect: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  dequal@2.0.3:
    dequal: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  detect-node@2.1.0:
    detect-node: private
  didyoumean@1.2.2:
    didyoumean: private
  dir-compare@3.3.0:
    dir-compare: private
  dir-glob@3.0.1:
    dir-glob: private
  dlv@1.1.3:
    dlv: private
  dmg-builder@24.13.3(electron-builder-squirrel-windows@24.13.3):
    dmg-builder: private
  dmg-license@1.0.11:
    dmg-license: private
  doctrine@3.0.0:
    doctrine: private
  dotenv-expand@5.1.0:
    dotenv-expand: private
  dotenv@9.0.2:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ejs@3.1.10:
    ejs: private
  electron-builder-squirrel-windows@24.13.3(dmg-builder@24.13.3):
    electron-builder-squirrel-windows: private
  electron-publish@24.13.1:
    electron-publish: private
  electron-to-chromium@1.5.195:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encoding@0.1.13:
    encoding: private
  end-of-stream@1.4.5:
    end-of-stream: private
  env-paths@2.2.1:
    env-paths: private
  err-code@2.0.3:
    err-code: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es6-error@4.1.1:
    es6-error: private
  esbuild@0.21.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  expand-template@2.0.3:
    expand-template: private
  exponential-backoff@3.1.2:
    exponential-backoff: private
  extract-zip@2.0.1:
    extract-zip: private
  extsprintf@1.4.1:
    extsprintf: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fd-slicer@1.1.0:
    fd-slicer: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  file-uri-to-path@1.0.0:
    file-uri-to-path: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.11:
    follow-redirects: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.4:
    form-data: private
  fraction.js@4.3.7:
    fraction.js: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gauge@4.0.4:
    gauge: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@5.2.0:
    get-stream: private
  github-from-package@0.0.0:
    github-from-package: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  global-agent@3.0.0:
    global-agent: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  got@11.8.6:
    got: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-unicode@2.0.1:
    has-unicode: private
  hasown@2.0.2:
    hasown: private
  hosted-git-info@4.1.0:
    hosted-git-info: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  http-proxy-agent@5.0.0:
    http-proxy-agent: private
  http2-wrapper@1.0.3:
    http2-wrapper: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  humanize-ms@1.2.1:
    humanize-ms: private
  iconv-corefoundation@1.1.7:
    iconv-corefoundation: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  infer-owner@1.0.4:
    infer-owner: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  ip-address@9.0.5:
    ip-address: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-ci@3.0.1:
    is-ci: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-interactive@1.0.0:
    is-interactive: private
  is-lambda@1.0.1:
    is-lambda: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  isarray@1.0.0:
    isarray: private
  isbinaryfile@5.0.4:
    isbinaryfile: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.4:
    jake: private
  jiti@1.21.7:
    jiti: private
  joi@17.13.3:
    joi: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  keyv@4.5.4:
    keyv: private
  lazy-val@1.0.5:
    lazy-val: private
  lazystream@1.0.1:
    lazystream: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.difference@4.5.0:
    lodash.difference: private
  lodash.flatten@4.4.0:
    lodash.flatten: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.union@4.6.0:
    lodash.union: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  loose-envify@1.4.0:
    loose-envify: private
  lowercase-keys@2.0.0:
    lowercase-keys: private
  lru-cache@5.1.1:
    lru-cache: private
  lzma-native@8.0.6:
    lzma-native: private
  make-cancellable-promise@1.3.2:
    make-cancellable-promise: private
  make-dir@3.1.0:
    make-dir: private
  make-event-props@1.6.2:
    make-event-props: private
  make-fetch-happen@9.1.0:
    make-fetch-happen: private
  matcher@3.0.0:
    matcher: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  merge-refs@1.3.0(@types/react@18.3.23):
    merge-refs: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@2.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mimic-response@3.1.0:
    mimic-response: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass-collect@1.0.2:
    minipass-collect: private
  minipass-fetch@1.4.1:
    minipass-fetch: private
  minipass-fetch@2.1.2:
    minipass-fetch: private
  minipass-flush@1.0.5:
    minipass-flush: private
  minipass-pipeline@1.2.4:
    minipass-pipeline: private
  minipass-sized@1.0.3:
    minipass-sized: private
  minipass@5.0.0:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mkdirp-classic@0.5.3:
    mkdirp-classic: private
  mkdirp@1.0.4:
    mkdirp: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  nan@2.23.0:
    nan: private
  nanoid@3.3.11:
    nanoid: private
  napi-build-utils@2.0.0:
    napi-build-utils: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.4:
    negotiator: private
  node-abi@3.75.0:
    node-abi: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-api-version@0.1.4:
    node-api-version: private
  node-fetch@2.7.0(encoding@0.1.13):
    node-fetch: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  node-gyp@8.4.1:
    node-gyp: private
  node-gyp@9.4.1:
    node-gyp: private
  node-releases@2.0.19:
    node-releases: private
  nopt@5.0.0:
    nopt: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  normalize-url@6.1.0:
    normalize-url: private
  npmlog@6.0.2:
    npmlog: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-keys@1.1.1:
    object-keys: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  ora@5.4.1:
    ora: private
  p-cancelable@2.1.1:
    p-cancelable: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@4.0.0:
    p-map: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  path2d-polyfill@2.0.1:
    path2d-polyfill: private
  pdfjs-dist@3.11.174(encoding@0.1.13):
    pdfjs-dist: private
  pend@1.2.0:
    pend: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  plist@3.1.0:
    plist: private
  postcss-import@15.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.6):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.6):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prebuild-install@7.1.3:
    prebuild-install: private
  prelude-ls@1.2.1:
    prelude-ls: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  progress@2.0.3:
    progress: private
  promise-inflight@1.0.1:
    promise-inflight: private
  promise-retry@2.0.1:
    promise-retry: private
  prop-types@15.8.1:
    prop-types: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  pump@3.0.3:
    pump: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-lru@5.1.1:
    quick-lru: private
  rc@1.2.8:
    rc: private
  react-is@16.13.1:
    react-is: private
  react-refresh@0.17.0:
    react-refresh: private
  react-remove-scroll-bar@2.3.8(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.1(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll: private
  react-style-singleton@2.2.3(@types/react@18.3.23)(react@18.3.1):
    react-style-singleton: private
  read-cache@1.0.0:
    read-cache: private
  read-config-file@6.3.2:
    read-config-file: private
  readable-stream@3.6.2:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  readdirp@3.6.0:
    readdirp: private
  require-directory@2.1.1:
    require-directory: private
  resolve-alpn@1.2.1:
    resolve-alpn: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve@1.22.10:
    resolve: private
  responselike@2.0.1:
    responselike: private
  restore-cursor@3.1.0:
    restore-cursor: private
  retry@0.12.0:
    retry: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  roarr@2.15.4:
    roarr: private
  rollup@4.46.2:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sanitize-filename@1.6.3:
    sanitize-filename: private
  sax@1.4.1:
    sax: private
  scheduler@0.23.2:
    scheduler: private
  semver-compare@1.0.0:
    semver-compare: private
  semver@7.7.2:
    semver: private
  serialize-error@7.0.1:
    serialize-error: private
  set-blocking@2.0.0:
    set-blocking: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-concat@1.0.1:
    simple-concat: private
  simple-get@4.0.1:
    simple-get: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  slash@3.0.0:
    slash: private
  slice-ansi@3.0.0:
    slice-ansi: private
  smart-buffer@4.2.0:
    smart-buffer: private
  socks-proxy-agent@6.2.1:
    socks-proxy-agent: private
  socks-proxy-agent@7.0.0:
    socks-proxy-agent: private
  socks@2.8.6:
    socks: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  spawn-command@0.0.2:
    spawn-command: private
  sprintf-js@1.1.3:
    sprintf-js: private
  ssri@8.0.1:
    ssri: private
  ssri@9.0.1:
    ssri: private
  stat-mode@1.0.0:
    stat-mode: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  sucrase@3.35.0:
    sucrase: private
  sumchecker@3.0.1:
    sumchecker: private
  supports-color@8.1.1:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tar-fs@2.1.3:
    tar-fs: private
  tar-stream@2.2.0:
    tar-stream: private
  tar@6.2.1:
    tar: private
  temp-file@3.4.0:
    temp-file: private
  text-table@0.2.0:
    text-table: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tmp-promise@3.0.3:
    tmp-promise: private
  tmp@0.2.3:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tr46@0.0.3:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  truncate-utf8-bytes@1.0.2:
    truncate-utf8-bytes: private
  ts-api-utils@1.4.3(typescript@5.9.2):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tslib@2.8.1:
    tslib: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  undici-types@6.21.0:
    undici-types: private
  unique-filename@1.1.1:
    unique-filename: private
  unique-slug@2.0.2:
    unique-slug: private
  universalify@2.0.1:
    universalify: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.3(@types/react@18.3.23)(react@18.3.1):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@18.3.23)(react@18.3.1):
    use-sidecar: private
  utf8-byte-length@1.0.5:
    utf8-byte-length: private
  util-deprecate@1.0.2:
    util-deprecate: private
  verror@1.10.1:
    verror: private
  warning@4.0.3:
    warning: private
  wcwidth@1.0.1:
    wcwidth: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  wide-align@1.1.5:
    wide-align: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  xmlbuilder@15.1.1:
    xmlbuilder: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zip-stream@4.1.1:
    zip-stream: private
ignoredBuilds:
  - lzma-native
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Tue, 05 Aug 2025 06:31:09 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-x64@0.21.5'
  - '@rollup/rollup-android-arm-eabi@4.46.2'
  - '@rollup/rollup-android-arm64@4.46.2'
  - '@rollup/rollup-darwin-x64@4.46.2'
  - '@rollup/rollup-freebsd-arm64@4.46.2'
  - '@rollup/rollup-freebsd-x64@4.46.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.2'
  - '@rollup/rollup-linux-arm64-gnu@4.46.2'
  - '@rollup/rollup-linux-arm64-musl@4.46.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.2'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-musl@4.46.2'
  - '@rollup/rollup-linux-s390x-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-musl@4.46.2'
  - '@rollup/rollup-win32-arm64-msvc@4.46.2'
  - '@rollup/rollup-win32-ia32-msvc@4.46.2'
  - '@rollup/rollup-win32-x64-msvc@4.46.2'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
