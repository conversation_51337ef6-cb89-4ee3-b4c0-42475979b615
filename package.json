{"name": "local-browser", "version": "1.0.0", "description": "本地学习客户端 - 支持视频、PDF等多媒体文件浏览和学习进度管理", "main": "dist/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "wait-on http://localhost:5173 && cross-env NODE_ENV=development electron .", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p electron/tsconfig.json", "rebuild": "electron-rebuild", "postinstall": "electron-rebuild", "build:electron": "npm run build && electron-builder", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "keywords": ["electron", "learning", "local", "video", "pdf", "education"], "author": "", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.0", "concurrently": "^8.0.0", "cross-env": "^7.0.3", "electron": "^28.0.0", "electron-builder": "^24.0.0", "electron-rebuild": "^3.2.9", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "postcss": "^8.4.0", "prebuild-install": "^7.1.3", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vite-plugin-electron": "^0.15.0", "wait-on": "^7.0.0"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.0", "@radix-ui/react-dropdown-menu": "^2.0.0", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.0", "@radix-ui/react-progress": "^1.0.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.0", "@radix-ui/react-slider": "^1.1.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.0", "@radix-ui/react-toast": "^1.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-pdf": "^7.5.0", "sqlite3": "^5.1.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.0"}, "build": {"appId": "com.localbrowser.app", "productName": "Local Browser", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*", "!node_modules/.cache/**/*"], "nodeGypRebuild": false, "buildDependenciesFromSource": false, "npmRebuild": false, "mac": {"category": "public.app-category.education", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}