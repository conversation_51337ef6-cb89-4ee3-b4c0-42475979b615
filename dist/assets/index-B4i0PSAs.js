var Hd=Object.defineProperty;var Wd=(e,t,n)=>t in e?Hd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var el=(e,t,n)=>Wd(e,typeof t!="symbol"?t+"":t,n);function Qd(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const l=Object.getOwnPropertyDescriptor(r,o);l&&Object.defineProperty(e,o,l.get?l:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const l of o)if(l.type==="childList")for(const s of l.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const l={};return o.integrity&&(l.integrity=o.integrity),o.referrerPolicy&&(l.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?l.credentials="include":o.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(o){if(o.ep)return;o.ep=!0;const l=n(o);fetch(o.href,l)}})();function Kd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ua={exports:{}},Ro={},Ha={exports:{}},U={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kr=Symbol.for("react.element"),Gd=Symbol.for("react.portal"),Xd=Symbol.for("react.fragment"),Zd=Symbol.for("react.strict_mode"),Yd=Symbol.for("react.profiler"),Jd=Symbol.for("react.provider"),qd=Symbol.for("react.context"),ef=Symbol.for("react.forward_ref"),tf=Symbol.for("react.suspense"),nf=Symbol.for("react.memo"),rf=Symbol.for("react.lazy"),ki=Symbol.iterator;function of(e){return e===null||typeof e!="object"?null:(e=ki&&e[ki]||e["@@iterator"],typeof e=="function"?e:null)}var Wa={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Qa=Object.assign,Ka={};function Mn(e,t,n){this.props=e,this.context=t,this.refs=Ka,this.updater=n||Wa}Mn.prototype.isReactComponent={};Mn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Mn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ga(){}Ga.prototype=Mn.prototype;function Es(e,t,n){this.props=e,this.context=t,this.refs=Ka,this.updater=n||Wa}var Ps=Es.prototype=new Ga;Ps.constructor=Es;Qa(Ps,Mn.prototype);Ps.isPureReactComponent=!0;var Ci=Array.isArray,Xa=Object.prototype.hasOwnProperty,zs={current:null},Za={key:!0,ref:!0,__self:!0,__source:!0};function Ya(e,t,n){var r,o={},l=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(l=""+t.key),t)Xa.call(t,r)&&!Za.hasOwnProperty(r)&&(o[r]=t[r]);var i=arguments.length-2;if(i===1)o.children=n;else if(1<i){for(var a=Array(i),c=0;c<i;c++)a[c]=arguments[c+2];o.children=a}if(e&&e.defaultProps)for(r in i=e.defaultProps,i)o[r]===void 0&&(o[r]=i[r]);return{$$typeof:kr,type:e,key:l,ref:s,props:o,_owner:zs.current}}function lf(e,t){return{$$typeof:kr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ds(e){return typeof e=="object"&&e!==null&&e.$$typeof===kr}function sf(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ni=/\/+/g;function tl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?sf(""+e.key):t.toString(36)}function Kr(e,t,n,r,o){var l=typeof e;(l==="undefined"||l==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case kr:case Gd:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+tl(s,0):r,Ci(o)?(n="",e!=null&&(n=e.replace(Ni,"$&/")+"/"),Kr(o,t,n,"",function(c){return c})):o!=null&&(Ds(o)&&(o=lf(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(Ni,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",Ci(e))for(var i=0;i<e.length;i++){l=e[i];var a=r+tl(l,i);s+=Kr(l,t,n,a,o)}else if(a=of(e),typeof a=="function")for(e=a.call(e),i=0;!(l=e.next()).done;)l=l.value,a=r+tl(l,i++),s+=Kr(l,t,n,a,o);else if(l==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Dr(e,t,n){if(e==null)return e;var r=[],o=0;return Kr(e,r,"","",function(l){return t.call(n,l,o++)}),r}function af(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var je={current:null},Gr={transition:null},uf={ReactCurrentDispatcher:je,ReactCurrentBatchConfig:Gr,ReactCurrentOwner:zs};function Ja(){throw Error("act(...) is not supported in production builds of React.")}U.Children={map:Dr,forEach:function(e,t,n){Dr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Dr(e,function(){t++}),t},toArray:function(e){return Dr(e,function(t){return t})||[]},only:function(e){if(!Ds(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};U.Component=Mn;U.Fragment=Xd;U.Profiler=Yd;U.PureComponent=Es;U.StrictMode=Zd;U.Suspense=tf;U.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=uf;U.act=Ja;U.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Qa({},e.props),o=e.key,l=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(l=t.ref,s=zs.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var i=e.type.defaultProps;for(a in t)Xa.call(t,a)&&!Za.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&i!==void 0?i[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){i=Array(a);for(var c=0;c<a;c++)i[c]=arguments[c+2];r.children=i}return{$$typeof:kr,type:e.type,key:o,ref:l,props:r,_owner:s}};U.createContext=function(e){return e={$$typeof:qd,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Jd,_context:e},e.Consumer=e};U.createElement=Ya;U.createFactory=function(e){var t=Ya.bind(null,e);return t.type=e,t};U.createRef=function(){return{current:null}};U.forwardRef=function(e){return{$$typeof:ef,render:e}};U.isValidElement=Ds;U.lazy=function(e){return{$$typeof:rf,_payload:{_status:-1,_result:e},_init:af}};U.memo=function(e,t){return{$$typeof:nf,type:e,compare:t===void 0?null:t}};U.startTransition=function(e){var t=Gr.transition;Gr.transition={};try{e()}finally{Gr.transition=t}};U.unstable_act=Ja;U.useCallback=function(e,t){return je.current.useCallback(e,t)};U.useContext=function(e){return je.current.useContext(e)};U.useDebugValue=function(){};U.useDeferredValue=function(e){return je.current.useDeferredValue(e)};U.useEffect=function(e,t){return je.current.useEffect(e,t)};U.useId=function(){return je.current.useId()};U.useImperativeHandle=function(e,t,n){return je.current.useImperativeHandle(e,t,n)};U.useInsertionEffect=function(e,t){return je.current.useInsertionEffect(e,t)};U.useLayoutEffect=function(e,t){return je.current.useLayoutEffect(e,t)};U.useMemo=function(e,t){return je.current.useMemo(e,t)};U.useReducer=function(e,t,n){return je.current.useReducer(e,t,n)};U.useRef=function(e){return je.current.useRef(e)};U.useState=function(e){return je.current.useState(e)};U.useSyncExternalStore=function(e,t,n){return je.current.useSyncExternalStore(e,t,n)};U.useTransition=function(){return je.current.useTransition()};U.version="18.3.1";Ha.exports=U;var x=Ha.exports;const xe=Kd(x),cf=Qd({__proto__:null,default:xe},[x]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var df=x,ff=Symbol.for("react.element"),pf=Symbol.for("react.fragment"),mf=Object.prototype.hasOwnProperty,hf=df.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,gf={key:!0,ref:!0,__self:!0,__source:!0};function qa(e,t,n){var r,o={},l=null,s=null;n!==void 0&&(l=""+n),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)mf.call(t,r)&&!gf.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:ff,type:e,key:l,ref:s,props:o,_owner:hf.current}}Ro.Fragment=pf;Ro.jsx=qa;Ro.jsxs=qa;Ua.exports=Ro;var u=Ua.exports,Dl={},eu={exports:{}},Fe={},tu={exports:{}},nu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(j,T){var F=j.length;j.push(T);e:for(;0<F;){var $=F-1>>>1,W=j[$];if(0<o(W,T))j[$]=T,j[F]=W,F=$;else break e}}function n(j){return j.length===0?null:j[0]}function r(j){if(j.length===0)return null;var T=j[0],F=j.pop();if(F!==T){j[0]=F;e:for(var $=0,W=j.length,Je=W>>>1;$<Je;){var Oe=2*($+1)-1,en=j[Oe],ot=Oe+1,tn=j[ot];if(0>o(en,F))ot<W&&0>o(tn,en)?(j[$]=tn,j[ot]=F,$=ot):(j[$]=en,j[Oe]=F,$=Oe);else if(ot<W&&0>o(tn,F))j[$]=tn,j[ot]=F,$=ot;else break e}}return T}function o(j,T){var F=j.sortIndex-T.sortIndex;return F!==0?F:j.id-T.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var s=Date,i=s.now();e.unstable_now=function(){return s.now()-i}}var a=[],c=[],g=1,h=null,p=3,y=!1,k=!1,w=!1,E=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(j){for(var T=n(c);T!==null;){if(T.callback===null)r(c);else if(T.startTime<=j)r(c),T.sortIndex=T.expirationTime,t(a,T);else break;T=n(c)}}function S(j){if(w=!1,m(j),!k)if(n(a)!==null)k=!0,O(N);else{var T=n(c);T!==null&&V(S,T.startTime-j)}}function N(j,T){k=!1,w&&(w=!1,f(D),D=-1),y=!0;var F=p;try{for(m(T),h=n(a);h!==null&&(!(h.expirationTime>T)||j&&!_());){var $=h.callback;if(typeof $=="function"){h.callback=null,p=h.priorityLevel;var W=$(h.expirationTime<=T);T=e.unstable_now(),typeof W=="function"?h.callback=W:h===n(a)&&r(a),m(T)}else r(a);h=n(a)}if(h!==null)var Je=!0;else{var Oe=n(c);Oe!==null&&V(S,Oe.startTime-T),Je=!1}return Je}finally{h=null,p=F,y=!1}}var z=!1,C=null,D=-1,A=5,v=-1;function _(){return!(e.unstable_now()-v<A)}function R(){if(C!==null){var j=e.unstable_now();v=j;var T=!0;try{T=C(!0,j)}finally{T?H():(z=!1,C=null)}}else z=!1}var H;if(typeof d=="function")H=function(){d(R)};else if(typeof MessageChannel<"u"){var X=new MessageChannel,I=X.port2;X.port1.onmessage=R,H=function(){I.postMessage(null)}}else H=function(){E(R,0)};function O(j){C=j,z||(z=!0,H())}function V(j,T){D=E(function(){j(e.unstable_now())},T)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(j){j.callback=null},e.unstable_continueExecution=function(){k||y||(k=!0,O(N))},e.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):A=0<j?Math.floor(1e3/j):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(j){switch(p){case 1:case 2:case 3:var T=3;break;default:T=p}var F=p;p=T;try{return j()}finally{p=F}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(j,T){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var F=p;p=j;try{return T()}finally{p=F}},e.unstable_scheduleCallback=function(j,T,F){var $=e.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?$+F:$):F=$,j){case 1:var W=-1;break;case 2:W=250;break;case 5:W=**********;break;case 4:W=1e4;break;default:W=5e3}return W=F+W,j={id:g++,callback:T,priorityLevel:j,startTime:F,expirationTime:W,sortIndex:-1},F>$?(j.sortIndex=F,t(c,j),n(a)===null&&j===n(c)&&(w?(f(D),D=-1):w=!0,V(S,F-$))):(j.sortIndex=W,t(a,j),k||y||(k=!0,O(N))),j},e.unstable_shouldYield=_,e.unstable_wrapCallback=function(j){var T=p;return function(){var F=p;p=T;try{return j.apply(this,arguments)}finally{p=F}}}})(nu);tu.exports=nu;var vf=tu.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yf=x,Re=vf;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ru=new Set,lr={};function Jt(e,t){Nn(e,t),Nn(e+"Capture",t)}function Nn(e,t){for(lr[e]=t,e=0;e<t.length;e++)ru.add(t[e])}var dt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),_l=Object.prototype.hasOwnProperty,xf=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ji={},Ei={};function wf(e){return _l.call(Ei,e)?!0:_l.call(ji,e)?!1:xf.test(e)?Ei[e]=!0:(ji[e]=!0,!1)}function Sf(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function kf(e,t,n,r){if(t===null||typeof t>"u"||Sf(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ee(e,t,n,r,o,l,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=s}var he={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){he[e]=new Ee(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];he[t]=new Ee(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){he[e]=new Ee(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){he[e]=new Ee(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){he[e]=new Ee(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){he[e]=new Ee(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){he[e]=new Ee(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){he[e]=new Ee(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){he[e]=new Ee(e,5,!1,e.toLowerCase(),null,!1,!1)});var _s=/[\-:]([a-z])/g;function Ms(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(_s,Ms);he[t]=new Ee(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(_s,Ms);he[t]=new Ee(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(_s,Ms);he[t]=new Ee(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){he[e]=new Ee(e,1,!1,e.toLowerCase(),null,!1,!1)});he.xlinkHref=new Ee("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){he[e]=new Ee(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ls(e,t,n,r){var o=he.hasOwnProperty(t)?he[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(kf(t,n,o,r)&&(n=null),r||o===null?wf(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var ht=yf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,_r=Symbol.for("react.element"),rn=Symbol.for("react.portal"),on=Symbol.for("react.fragment"),Ts=Symbol.for("react.strict_mode"),Ml=Symbol.for("react.profiler"),ou=Symbol.for("react.provider"),lu=Symbol.for("react.context"),Is=Symbol.for("react.forward_ref"),Ll=Symbol.for("react.suspense"),Tl=Symbol.for("react.suspense_list"),Rs=Symbol.for("react.memo"),xt=Symbol.for("react.lazy"),su=Symbol.for("react.offscreen"),Pi=Symbol.iterator;function Fn(e){return e===null||typeof e!="object"?null:(e=Pi&&e[Pi]||e["@@iterator"],typeof e=="function"?e:null)}var oe=Object.assign,nl;function Qn(e){if(nl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);nl=t&&t[1]||""}return`
`+nl+e}var rl=!1;function ol(e,t){if(!e||rl)return"";rl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var o=c.stack.split(`
`),l=r.stack.split(`
`),s=o.length-1,i=l.length-1;1<=s&&0<=i&&o[s]!==l[i];)i--;for(;1<=s&&0<=i;s--,i--)if(o[s]!==l[i]){if(s!==1||i!==1)do if(s--,i--,0>i||o[s]!==l[i]){var a=`
`+o[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=i);break}}}finally{rl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Qn(e):""}function Cf(e){switch(e.tag){case 5:return Qn(e.type);case 16:return Qn("Lazy");case 13:return Qn("Suspense");case 19:return Qn("SuspenseList");case 0:case 2:case 15:return e=ol(e.type,!1),e;case 11:return e=ol(e.type.render,!1),e;case 1:return e=ol(e.type,!0),e;default:return""}}function Il(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case on:return"Fragment";case rn:return"Portal";case Ml:return"Profiler";case Ts:return"StrictMode";case Ll:return"Suspense";case Tl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case lu:return(e.displayName||"Context")+".Consumer";case ou:return(e._context.displayName||"Context")+".Provider";case Is:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Rs:return t=e.displayName||null,t!==null?t:Il(e.type)||"Memo";case xt:t=e._payload,e=e._init;try{return Il(e(t))}catch{}}return null}function Nf(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Il(t);case 8:return t===Ts?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Tt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function iu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function jf(e){var t=iu(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,l.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Mr(e){e._valueTracker||(e._valueTracker=jf(e))}function au(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=iu(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function lo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Rl(e,t){var n=t.checked;return oe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function zi(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Tt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function uu(e,t){t=t.checked,t!=null&&Ls(e,"checked",t,!1)}function Fl(e,t){uu(e,t);var n=Tt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Al(e,t.type,n):t.hasOwnProperty("defaultValue")&&Al(e,t.type,Tt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Di(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Al(e,t,n){(t!=="number"||lo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Kn=Array.isArray;function vn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Tt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Ol(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(P(91));return oe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function _i(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(P(92));if(Kn(n)){if(1<n.length)throw Error(P(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Tt(n)}}function cu(e,t){var n=Tt(t.value),r=Tt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Mi(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function du(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Vl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?du(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Lr,fu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Lr=Lr||document.createElement("div"),Lr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Lr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function sr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Zn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ef=["Webkit","ms","Moz","O"];Object.keys(Zn).forEach(function(e){Ef.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Zn[t]=Zn[e]})});function pu(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Zn.hasOwnProperty(e)&&Zn[e]?(""+t).trim():t+"px"}function mu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=pu(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Pf=oe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function bl(e,t){if(t){if(Pf[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(P(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(P(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(P(61))}if(t.style!=null&&typeof t.style!="object")throw Error(P(62))}}function $l(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Bl=null;function Fs(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ul=null,yn=null,xn=null;function Li(e){if(e=jr(e)){if(typeof Ul!="function")throw Error(P(280));var t=e.stateNode;t&&(t=bo(t),Ul(e.stateNode,e.type,t))}}function hu(e){yn?xn?xn.push(e):xn=[e]:yn=e}function gu(){if(yn){var e=yn,t=xn;if(xn=yn=null,Li(e),t)for(e=0;e<t.length;e++)Li(t[e])}}function vu(e,t){return e(t)}function yu(){}var ll=!1;function xu(e,t,n){if(ll)return e(t,n);ll=!0;try{return vu(e,t,n)}finally{ll=!1,(yn!==null||xn!==null)&&(yu(),gu())}}function ir(e,t){var n=e.stateNode;if(n===null)return null;var r=bo(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(P(231,t,typeof n));return n}var Hl=!1;if(dt)try{var An={};Object.defineProperty(An,"passive",{get:function(){Hl=!0}}),window.addEventListener("test",An,An),window.removeEventListener("test",An,An)}catch{Hl=!1}function zf(e,t,n,r,o,l,s,i,a){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(g){this.onError(g)}}var Yn=!1,so=null,io=!1,Wl=null,Df={onError:function(e){Yn=!0,so=e}};function _f(e,t,n,r,o,l,s,i,a){Yn=!1,so=null,zf.apply(Df,arguments)}function Mf(e,t,n,r,o,l,s,i,a){if(_f.apply(this,arguments),Yn){if(Yn){var c=so;Yn=!1,so=null}else throw Error(P(198));io||(io=!0,Wl=c)}}function qt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function wu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ti(e){if(qt(e)!==e)throw Error(P(188))}function Lf(e){var t=e.alternate;if(!t){if(t=qt(e),t===null)throw Error(P(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var l=o.alternate;if(l===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===l.child){for(l=o.child;l;){if(l===n)return Ti(o),e;if(l===r)return Ti(o),t;l=l.sibling}throw Error(P(188))}if(n.return!==r.return)n=o,r=l;else{for(var s=!1,i=o.child;i;){if(i===n){s=!0,n=o,r=l;break}if(i===r){s=!0,r=o,n=l;break}i=i.sibling}if(!s){for(i=l.child;i;){if(i===n){s=!0,n=l,r=o;break}if(i===r){s=!0,r=l,n=o;break}i=i.sibling}if(!s)throw Error(P(189))}}if(n.alternate!==r)throw Error(P(190))}if(n.tag!==3)throw Error(P(188));return n.stateNode.current===n?e:t}function Su(e){return e=Lf(e),e!==null?ku(e):null}function ku(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ku(e);if(t!==null)return t;e=e.sibling}return null}var Cu=Re.unstable_scheduleCallback,Ii=Re.unstable_cancelCallback,Tf=Re.unstable_shouldYield,If=Re.unstable_requestPaint,ie=Re.unstable_now,Rf=Re.unstable_getCurrentPriorityLevel,As=Re.unstable_ImmediatePriority,Nu=Re.unstable_UserBlockingPriority,ao=Re.unstable_NormalPriority,Ff=Re.unstable_LowPriority,ju=Re.unstable_IdlePriority,Fo=null,nt=null;function Af(e){if(nt&&typeof nt.onCommitFiberRoot=="function")try{nt.onCommitFiberRoot(Fo,e,void 0,(e.current.flags&128)===128)}catch{}}var Xe=Math.clz32?Math.clz32:bf,Of=Math.log,Vf=Math.LN2;function bf(e){return e>>>=0,e===0?32:31-(Of(e)/Vf|0)|0}var Tr=64,Ir=4194304;function Gn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function uo(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,l=e.pingedLanes,s=n&268435455;if(s!==0){var i=s&~o;i!==0?r=Gn(i):(l&=s,l!==0&&(r=Gn(l)))}else s=n&~o,s!==0?r=Gn(s):l!==0&&(r=Gn(l));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,l=t&-t,o>=l||o===16&&(l&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Xe(t),o=1<<n,r|=e[n],t&=~o;return r}function $f(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Bf(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,l=e.pendingLanes;0<l;){var s=31-Xe(l),i=1<<s,a=o[s];a===-1?(!(i&n)||i&r)&&(o[s]=$f(i,t)):a<=t&&(e.expiredLanes|=i),l&=~i}}function Ql(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Eu(){var e=Tr;return Tr<<=1,!(Tr&4194240)&&(Tr=64),e}function sl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Cr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Xe(t),e[t]=n}function Uf(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Xe(n),l=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~l}}function Os(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Xe(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var K=0;function Pu(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var zu,Vs,Du,_u,Mu,Kl=!1,Rr=[],jt=null,Et=null,Pt=null,ar=new Map,ur=new Map,St=[],Hf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ri(e,t){switch(e){case"focusin":case"focusout":jt=null;break;case"dragenter":case"dragleave":Et=null;break;case"mouseover":case"mouseout":Pt=null;break;case"pointerover":case"pointerout":ar.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ur.delete(t.pointerId)}}function On(e,t,n,r,o,l){return e===null||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[o]},t!==null&&(t=jr(t),t!==null&&Vs(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Wf(e,t,n,r,o){switch(t){case"focusin":return jt=On(jt,e,t,n,r,o),!0;case"dragenter":return Et=On(Et,e,t,n,r,o),!0;case"mouseover":return Pt=On(Pt,e,t,n,r,o),!0;case"pointerover":var l=o.pointerId;return ar.set(l,On(ar.get(l)||null,e,t,n,r,o)),!0;case"gotpointercapture":return l=o.pointerId,ur.set(l,On(ur.get(l)||null,e,t,n,r,o)),!0}return!1}function Lu(e){var t=$t(e.target);if(t!==null){var n=qt(t);if(n!==null){if(t=n.tag,t===13){if(t=wu(n),t!==null){e.blockedOn=t,Mu(e.priority,function(){Du(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Xr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Bl=r,n.target.dispatchEvent(r),Bl=null}else return t=jr(n),t!==null&&Vs(t),e.blockedOn=n,!1;t.shift()}return!0}function Fi(e,t,n){Xr(e)&&n.delete(t)}function Qf(){Kl=!1,jt!==null&&Xr(jt)&&(jt=null),Et!==null&&Xr(Et)&&(Et=null),Pt!==null&&Xr(Pt)&&(Pt=null),ar.forEach(Fi),ur.forEach(Fi)}function Vn(e,t){e.blockedOn===t&&(e.blockedOn=null,Kl||(Kl=!0,Re.unstable_scheduleCallback(Re.unstable_NormalPriority,Qf)))}function cr(e){function t(o){return Vn(o,e)}if(0<Rr.length){Vn(Rr[0],e);for(var n=1;n<Rr.length;n++){var r=Rr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(jt!==null&&Vn(jt,e),Et!==null&&Vn(Et,e),Pt!==null&&Vn(Pt,e),ar.forEach(t),ur.forEach(t),n=0;n<St.length;n++)r=St[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<St.length&&(n=St[0],n.blockedOn===null);)Lu(n),n.blockedOn===null&&St.shift()}var wn=ht.ReactCurrentBatchConfig,co=!0;function Kf(e,t,n,r){var o=K,l=wn.transition;wn.transition=null;try{K=1,bs(e,t,n,r)}finally{K=o,wn.transition=l}}function Gf(e,t,n,r){var o=K,l=wn.transition;wn.transition=null;try{K=4,bs(e,t,n,r)}finally{K=o,wn.transition=l}}function bs(e,t,n,r){if(co){var o=Gl(e,t,n,r);if(o===null)gl(e,t,r,fo,n),Ri(e,r);else if(Wf(o,e,t,n,r))r.stopPropagation();else if(Ri(e,r),t&4&&-1<Hf.indexOf(e)){for(;o!==null;){var l=jr(o);if(l!==null&&zu(l),l=Gl(e,t,n,r),l===null&&gl(e,t,r,fo,n),l===o)break;o=l}o!==null&&r.stopPropagation()}else gl(e,t,r,null,n)}}var fo=null;function Gl(e,t,n,r){if(fo=null,e=Fs(r),e=$t(e),e!==null)if(t=qt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=wu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return fo=e,null}function Tu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Rf()){case As:return 1;case Nu:return 4;case ao:case Ff:return 16;case ju:return 536870912;default:return 16}default:return 16}}var Ct=null,$s=null,Zr=null;function Iu(){if(Zr)return Zr;var e,t=$s,n=t.length,r,o="value"in Ct?Ct.value:Ct.textContent,l=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[l-r];r++);return Zr=o.slice(e,1<r?1-r:void 0)}function Yr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Fr(){return!0}function Ai(){return!1}function Ae(e){function t(n,r,o,l,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=l,this.target=s,this.currentTarget=null;for(var i in e)e.hasOwnProperty(i)&&(n=e[i],this[i]=n?n(l):l[i]);return this.isDefaultPrevented=(l.defaultPrevented!=null?l.defaultPrevented:l.returnValue===!1)?Fr:Ai,this.isPropagationStopped=Ai,this}return oe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Fr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Fr)},persist:function(){},isPersistent:Fr}),t}var Ln={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Bs=Ae(Ln),Nr=oe({},Ln,{view:0,detail:0}),Xf=Ae(Nr),il,al,bn,Ao=oe({},Nr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Us,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==bn&&(bn&&e.type==="mousemove"?(il=e.screenX-bn.screenX,al=e.screenY-bn.screenY):al=il=0,bn=e),il)},movementY:function(e){return"movementY"in e?e.movementY:al}}),Oi=Ae(Ao),Zf=oe({},Ao,{dataTransfer:0}),Yf=Ae(Zf),Jf=oe({},Nr,{relatedTarget:0}),ul=Ae(Jf),qf=oe({},Ln,{animationName:0,elapsedTime:0,pseudoElement:0}),ep=Ae(qf),tp=oe({},Ln,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),np=Ae(tp),rp=oe({},Ln,{data:0}),Vi=Ae(rp),op={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},lp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},sp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ip(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=sp[e])?!!t[e]:!1}function Us(){return ip}var ap=oe({},Nr,{key:function(e){if(e.key){var t=op[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Yr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?lp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Us,charCode:function(e){return e.type==="keypress"?Yr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Yr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),up=Ae(ap),cp=oe({},Ao,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),bi=Ae(cp),dp=oe({},Nr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Us}),fp=Ae(dp),pp=oe({},Ln,{propertyName:0,elapsedTime:0,pseudoElement:0}),mp=Ae(pp),hp=oe({},Ao,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),gp=Ae(hp),vp=[9,13,27,32],Hs=dt&&"CompositionEvent"in window,Jn=null;dt&&"documentMode"in document&&(Jn=document.documentMode);var yp=dt&&"TextEvent"in window&&!Jn,Ru=dt&&(!Hs||Jn&&8<Jn&&11>=Jn),$i=" ",Bi=!1;function Fu(e,t){switch(e){case"keyup":return vp.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Au(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ln=!1;function xp(e,t){switch(e){case"compositionend":return Au(t);case"keypress":return t.which!==32?null:(Bi=!0,$i);case"textInput":return e=t.data,e===$i&&Bi?null:e;default:return null}}function wp(e,t){if(ln)return e==="compositionend"||!Hs&&Fu(e,t)?(e=Iu(),Zr=$s=Ct=null,ln=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ru&&t.locale!=="ko"?null:t.data;default:return null}}var Sp={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ui(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Sp[e.type]:t==="textarea"}function Ou(e,t,n,r){hu(r),t=po(t,"onChange"),0<t.length&&(n=new Bs("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qn=null,dr=null;function kp(e){Xu(e,0)}function Oo(e){var t=un(e);if(au(t))return e}function Cp(e,t){if(e==="change")return t}var Vu=!1;if(dt){var cl;if(dt){var dl="oninput"in document;if(!dl){var Hi=document.createElement("div");Hi.setAttribute("oninput","return;"),dl=typeof Hi.oninput=="function"}cl=dl}else cl=!1;Vu=cl&&(!document.documentMode||9<document.documentMode)}function Wi(){qn&&(qn.detachEvent("onpropertychange",bu),dr=qn=null)}function bu(e){if(e.propertyName==="value"&&Oo(dr)){var t=[];Ou(t,dr,e,Fs(e)),xu(kp,t)}}function Np(e,t,n){e==="focusin"?(Wi(),qn=t,dr=n,qn.attachEvent("onpropertychange",bu)):e==="focusout"&&Wi()}function jp(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Oo(dr)}function Ep(e,t){if(e==="click")return Oo(t)}function Pp(e,t){if(e==="input"||e==="change")return Oo(t)}function zp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ye=typeof Object.is=="function"?Object.is:zp;function fr(e,t){if(Ye(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!_l.call(t,o)||!Ye(e[o],t[o]))return!1}return!0}function Qi(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ki(e,t){var n=Qi(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Qi(n)}}function $u(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?$u(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Bu(){for(var e=window,t=lo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=lo(e.document)}return t}function Ws(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Dp(e){var t=Bu(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&$u(n.ownerDocument.documentElement,n)){if(r!==null&&Ws(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,l=Math.min(r.start,o);r=r.end===void 0?l:Math.min(r.end,o),!e.extend&&l>r&&(o=r,r=l,l=o),o=Ki(n,l);var s=Ki(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var _p=dt&&"documentMode"in document&&11>=document.documentMode,sn=null,Xl=null,er=null,Zl=!1;function Gi(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Zl||sn==null||sn!==lo(r)||(r=sn,"selectionStart"in r&&Ws(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),er&&fr(er,r)||(er=r,r=po(Xl,"onSelect"),0<r.length&&(t=new Bs("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=sn)))}function Ar(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var an={animationend:Ar("Animation","AnimationEnd"),animationiteration:Ar("Animation","AnimationIteration"),animationstart:Ar("Animation","AnimationStart"),transitionend:Ar("Transition","TransitionEnd")},fl={},Uu={};dt&&(Uu=document.createElement("div").style,"AnimationEvent"in window||(delete an.animationend.animation,delete an.animationiteration.animation,delete an.animationstart.animation),"TransitionEvent"in window||delete an.transitionend.transition);function Vo(e){if(fl[e])return fl[e];if(!an[e])return e;var t=an[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Uu)return fl[e]=t[n];return e}var Hu=Vo("animationend"),Wu=Vo("animationiteration"),Qu=Vo("animationstart"),Ku=Vo("transitionend"),Gu=new Map,Xi="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ft(e,t){Gu.set(e,t),Jt(t,[e])}for(var pl=0;pl<Xi.length;pl++){var ml=Xi[pl],Mp=ml.toLowerCase(),Lp=ml[0].toUpperCase()+ml.slice(1);Ft(Mp,"on"+Lp)}Ft(Hu,"onAnimationEnd");Ft(Wu,"onAnimationIteration");Ft(Qu,"onAnimationStart");Ft("dblclick","onDoubleClick");Ft("focusin","onFocus");Ft("focusout","onBlur");Ft(Ku,"onTransitionEnd");Nn("onMouseEnter",["mouseout","mouseover"]);Nn("onMouseLeave",["mouseout","mouseover"]);Nn("onPointerEnter",["pointerout","pointerover"]);Nn("onPointerLeave",["pointerout","pointerover"]);Jt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Jt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Jt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Jt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Jt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Jt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Xn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Tp=new Set("cancel close invalid load scroll toggle".split(" ").concat(Xn));function Zi(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Mf(r,t,void 0,e),e.currentTarget=null}function Xu(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var s=r.length-1;0<=s;s--){var i=r[s],a=i.instance,c=i.currentTarget;if(i=i.listener,a!==l&&o.isPropagationStopped())break e;Zi(o,i,c),l=a}else for(s=0;s<r.length;s++){if(i=r[s],a=i.instance,c=i.currentTarget,i=i.listener,a!==l&&o.isPropagationStopped())break e;Zi(o,i,c),l=a}}}if(io)throw e=Wl,io=!1,Wl=null,e}function q(e,t){var n=t[ts];n===void 0&&(n=t[ts]=new Set);var r=e+"__bubble";n.has(r)||(Zu(t,e,2,!1),n.add(r))}function hl(e,t,n){var r=0;t&&(r|=4),Zu(n,e,r,t)}var Or="_reactListening"+Math.random().toString(36).slice(2);function pr(e){if(!e[Or]){e[Or]=!0,ru.forEach(function(n){n!=="selectionchange"&&(Tp.has(n)||hl(n,!1,e),hl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Or]||(t[Or]=!0,hl("selectionchange",!1,t))}}function Zu(e,t,n,r){switch(Tu(t)){case 1:var o=Kf;break;case 4:o=Gf;break;default:o=bs}n=o.bind(null,t,n,e),o=void 0,!Hl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function gl(e,t,n,r,o){var l=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var i=r.stateNode.containerInfo;if(i===o||i.nodeType===8&&i.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;s=s.return}for(;i!==null;){if(s=$t(i),s===null)return;if(a=s.tag,a===5||a===6){r=l=s;continue e}i=i.parentNode}}r=r.return}xu(function(){var c=l,g=Fs(n),h=[];e:{var p=Gu.get(e);if(p!==void 0){var y=Bs,k=e;switch(e){case"keypress":if(Yr(n)===0)break e;case"keydown":case"keyup":y=up;break;case"focusin":k="focus",y=ul;break;case"focusout":k="blur",y=ul;break;case"beforeblur":case"afterblur":y=ul;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=Oi;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=Yf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=fp;break;case Hu:case Wu:case Qu:y=ep;break;case Ku:y=mp;break;case"scroll":y=Xf;break;case"wheel":y=gp;break;case"copy":case"cut":case"paste":y=np;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=bi}var w=(t&4)!==0,E=!w&&e==="scroll",f=w?p!==null?p+"Capture":null:p;w=[];for(var d=c,m;d!==null;){m=d;var S=m.stateNode;if(m.tag===5&&S!==null&&(m=S,f!==null&&(S=ir(d,f),S!=null&&w.push(mr(d,S,m)))),E)break;d=d.return}0<w.length&&(p=new y(p,k,null,n,g),h.push({event:p,listeners:w}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",p&&n!==Bl&&(k=n.relatedTarget||n.fromElement)&&($t(k)||k[ft]))break e;if((y||p)&&(p=g.window===g?g:(p=g.ownerDocument)?p.defaultView||p.parentWindow:window,y?(k=n.relatedTarget||n.toElement,y=c,k=k?$t(k):null,k!==null&&(E=qt(k),k!==E||k.tag!==5&&k.tag!==6)&&(k=null)):(y=null,k=c),y!==k)){if(w=Oi,S="onMouseLeave",f="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(w=bi,S="onPointerLeave",f="onPointerEnter",d="pointer"),E=y==null?p:un(y),m=k==null?p:un(k),p=new w(S,d+"leave",y,n,g),p.target=E,p.relatedTarget=m,S=null,$t(g)===c&&(w=new w(f,d+"enter",k,n,g),w.target=m,w.relatedTarget=E,S=w),E=S,y&&k)t:{for(w=y,f=k,d=0,m=w;m;m=nn(m))d++;for(m=0,S=f;S;S=nn(S))m++;for(;0<d-m;)w=nn(w),d--;for(;0<m-d;)f=nn(f),m--;for(;d--;){if(w===f||f!==null&&w===f.alternate)break t;w=nn(w),f=nn(f)}w=null}else w=null;y!==null&&Yi(h,p,y,w,!1),k!==null&&E!==null&&Yi(h,E,k,w,!0)}}e:{if(p=c?un(c):window,y=p.nodeName&&p.nodeName.toLowerCase(),y==="select"||y==="input"&&p.type==="file")var N=Cp;else if(Ui(p))if(Vu)N=Pp;else{N=jp;var z=Np}else(y=p.nodeName)&&y.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(N=Ep);if(N&&(N=N(e,c))){Ou(h,N,n,g);break e}z&&z(e,p,c),e==="focusout"&&(z=p._wrapperState)&&z.controlled&&p.type==="number"&&Al(p,"number",p.value)}switch(z=c?un(c):window,e){case"focusin":(Ui(z)||z.contentEditable==="true")&&(sn=z,Xl=c,er=null);break;case"focusout":er=Xl=sn=null;break;case"mousedown":Zl=!0;break;case"contextmenu":case"mouseup":case"dragend":Zl=!1,Gi(h,n,g);break;case"selectionchange":if(_p)break;case"keydown":case"keyup":Gi(h,n,g)}var C;if(Hs)e:{switch(e){case"compositionstart":var D="onCompositionStart";break e;case"compositionend":D="onCompositionEnd";break e;case"compositionupdate":D="onCompositionUpdate";break e}D=void 0}else ln?Fu(e,n)&&(D="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(D="onCompositionStart");D&&(Ru&&n.locale!=="ko"&&(ln||D!=="onCompositionStart"?D==="onCompositionEnd"&&ln&&(C=Iu()):(Ct=g,$s="value"in Ct?Ct.value:Ct.textContent,ln=!0)),z=po(c,D),0<z.length&&(D=new Vi(D,e,null,n,g),h.push({event:D,listeners:z}),C?D.data=C:(C=Au(n),C!==null&&(D.data=C)))),(C=yp?xp(e,n):wp(e,n))&&(c=po(c,"onBeforeInput"),0<c.length&&(g=new Vi("onBeforeInput","beforeinput",null,n,g),h.push({event:g,listeners:c}),g.data=C))}Xu(h,t)})}function mr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function po(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,l=o.stateNode;o.tag===5&&l!==null&&(o=l,l=ir(e,n),l!=null&&r.unshift(mr(e,l,o)),l=ir(e,t),l!=null&&r.push(mr(e,l,o))),e=e.return}return r}function nn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Yi(e,t,n,r,o){for(var l=t._reactName,s=[];n!==null&&n!==r;){var i=n,a=i.alternate,c=i.stateNode;if(a!==null&&a===r)break;i.tag===5&&c!==null&&(i=c,o?(a=ir(n,l),a!=null&&s.unshift(mr(n,a,i))):o||(a=ir(n,l),a!=null&&s.push(mr(n,a,i)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Ip=/\r\n?/g,Rp=/\u0000|\uFFFD/g;function Ji(e){return(typeof e=="string"?e:""+e).replace(Ip,`
`).replace(Rp,"")}function Vr(e,t,n){if(t=Ji(t),Ji(e)!==t&&n)throw Error(P(425))}function mo(){}var Yl=null,Jl=null;function ql(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var es=typeof setTimeout=="function"?setTimeout:void 0,Fp=typeof clearTimeout=="function"?clearTimeout:void 0,qi=typeof Promise=="function"?Promise:void 0,Ap=typeof queueMicrotask=="function"?queueMicrotask:typeof qi<"u"?function(e){return qi.resolve(null).then(e).catch(Op)}:es;function Op(e){setTimeout(function(){throw e})}function vl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),cr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);cr(t)}function zt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ea(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Tn=Math.random().toString(36).slice(2),tt="__reactFiber$"+Tn,hr="__reactProps$"+Tn,ft="__reactContainer$"+Tn,ts="__reactEvents$"+Tn,Vp="__reactListeners$"+Tn,bp="__reactHandles$"+Tn;function $t(e){var t=e[tt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ft]||n[tt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ea(e);e!==null;){if(n=e[tt])return n;e=ea(e)}return t}e=n,n=e.parentNode}return null}function jr(e){return e=e[tt]||e[ft],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function un(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(P(33))}function bo(e){return e[hr]||null}var ns=[],cn=-1;function At(e){return{current:e}}function ee(e){0>cn||(e.current=ns[cn],ns[cn]=null,cn--)}function Y(e,t){cn++,ns[cn]=e.current,e.current=t}var It={},Se=At(It),De=At(!1),Qt=It;function jn(e,t){var n=e.type.contextTypes;if(!n)return It;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},l;for(l in n)o[l]=t[l];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function _e(e){return e=e.childContextTypes,e!=null}function ho(){ee(De),ee(Se)}function ta(e,t,n){if(Se.current!==It)throw Error(P(168));Y(Se,t),Y(De,n)}function Yu(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(P(108,Nf(e)||"Unknown",o));return oe({},n,r)}function go(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||It,Qt=Se.current,Y(Se,e),Y(De,De.current),!0}function na(e,t,n){var r=e.stateNode;if(!r)throw Error(P(169));n?(e=Yu(e,t,Qt),r.__reactInternalMemoizedMergedChildContext=e,ee(De),ee(Se),Y(Se,e)):ee(De),Y(De,n)}var it=null,$o=!1,yl=!1;function Ju(e){it===null?it=[e]:it.push(e)}function $p(e){$o=!0,Ju(e)}function Ot(){if(!yl&&it!==null){yl=!0;var e=0,t=K;try{var n=it;for(K=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}it=null,$o=!1}catch(o){throw it!==null&&(it=it.slice(e+1)),Cu(As,Ot),o}finally{K=t,yl=!1}}return null}var dn=[],fn=0,vo=null,yo=0,Ve=[],be=0,Kt=null,at=1,ut="";function Vt(e,t){dn[fn++]=yo,dn[fn++]=vo,vo=e,yo=t}function qu(e,t,n){Ve[be++]=at,Ve[be++]=ut,Ve[be++]=Kt,Kt=e;var r=at;e=ut;var o=32-Xe(r)-1;r&=~(1<<o),n+=1;var l=32-Xe(t)+o;if(30<l){var s=o-o%5;l=(r&(1<<s)-1).toString(32),r>>=s,o-=s,at=1<<32-Xe(t)+o|n<<o|r,ut=l+e}else at=1<<l|n<<o|r,ut=e}function Qs(e){e.return!==null&&(Vt(e,1),qu(e,1,0))}function Ks(e){for(;e===vo;)vo=dn[--fn],dn[fn]=null,yo=dn[--fn],dn[fn]=null;for(;e===Kt;)Kt=Ve[--be],Ve[be]=null,ut=Ve[--be],Ve[be]=null,at=Ve[--be],Ve[be]=null}var Ie=null,Te=null,te=!1,Ge=null;function ec(e,t){var n=$e(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ra(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ie=e,Te=zt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ie=e,Te=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Kt!==null?{id:at,overflow:ut}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=$e(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ie=e,Te=null,!0):!1;default:return!1}}function rs(e){return(e.mode&1)!==0&&(e.flags&128)===0}function os(e){if(te){var t=Te;if(t){var n=t;if(!ra(e,t)){if(rs(e))throw Error(P(418));t=zt(n.nextSibling);var r=Ie;t&&ra(e,t)?ec(r,n):(e.flags=e.flags&-4097|2,te=!1,Ie=e)}}else{if(rs(e))throw Error(P(418));e.flags=e.flags&-4097|2,te=!1,Ie=e}}}function oa(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ie=e}function br(e){if(e!==Ie)return!1;if(!te)return oa(e),te=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ql(e.type,e.memoizedProps)),t&&(t=Te)){if(rs(e))throw tc(),Error(P(418));for(;t;)ec(e,t),t=zt(t.nextSibling)}if(oa(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(P(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Te=zt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Te=null}}else Te=Ie?zt(e.stateNode.nextSibling):null;return!0}function tc(){for(var e=Te;e;)e=zt(e.nextSibling)}function En(){Te=Ie=null,te=!1}function Gs(e){Ge===null?Ge=[e]:Ge.push(e)}var Bp=ht.ReactCurrentBatchConfig;function $n(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(P(309));var r=n.stateNode}if(!r)throw Error(P(147,e));var o=r,l=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===l?t.ref:(t=function(s){var i=o.refs;s===null?delete i[l]:i[l]=s},t._stringRef=l,t)}if(typeof e!="string")throw Error(P(284));if(!n._owner)throw Error(P(290,e))}return e}function $r(e,t){throw e=Object.prototype.toString.call(t),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function la(e){var t=e._init;return t(e._payload)}function nc(e){function t(f,d){if(e){var m=f.deletions;m===null?(f.deletions=[d],f.flags|=16):m.push(d)}}function n(f,d){if(!e)return null;for(;d!==null;)t(f,d),d=d.sibling;return null}function r(f,d){for(f=new Map;d!==null;)d.key!==null?f.set(d.key,d):f.set(d.index,d),d=d.sibling;return f}function o(f,d){return f=Lt(f,d),f.index=0,f.sibling=null,f}function l(f,d,m){return f.index=m,e?(m=f.alternate,m!==null?(m=m.index,m<d?(f.flags|=2,d):m):(f.flags|=2,d)):(f.flags|=1048576,d)}function s(f){return e&&f.alternate===null&&(f.flags|=2),f}function i(f,d,m,S){return d===null||d.tag!==6?(d=jl(m,f.mode,S),d.return=f,d):(d=o(d,m),d.return=f,d)}function a(f,d,m,S){var N=m.type;return N===on?g(f,d,m.props.children,S,m.key):d!==null&&(d.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===xt&&la(N)===d.type)?(S=o(d,m.props),S.ref=$n(f,d,m),S.return=f,S):(S=oo(m.type,m.key,m.props,null,f.mode,S),S.ref=$n(f,d,m),S.return=f,S)}function c(f,d,m,S){return d===null||d.tag!==4||d.stateNode.containerInfo!==m.containerInfo||d.stateNode.implementation!==m.implementation?(d=El(m,f.mode,S),d.return=f,d):(d=o(d,m.children||[]),d.return=f,d)}function g(f,d,m,S,N){return d===null||d.tag!==7?(d=Wt(m,f.mode,S,N),d.return=f,d):(d=o(d,m),d.return=f,d)}function h(f,d,m){if(typeof d=="string"&&d!==""||typeof d=="number")return d=jl(""+d,f.mode,m),d.return=f,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case _r:return m=oo(d.type,d.key,d.props,null,f.mode,m),m.ref=$n(f,null,d),m.return=f,m;case rn:return d=El(d,f.mode,m),d.return=f,d;case xt:var S=d._init;return h(f,S(d._payload),m)}if(Kn(d)||Fn(d))return d=Wt(d,f.mode,m,null),d.return=f,d;$r(f,d)}return null}function p(f,d,m,S){var N=d!==null?d.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return N!==null?null:i(f,d,""+m,S);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case _r:return m.key===N?a(f,d,m,S):null;case rn:return m.key===N?c(f,d,m,S):null;case xt:return N=m._init,p(f,d,N(m._payload),S)}if(Kn(m)||Fn(m))return N!==null?null:g(f,d,m,S,null);$r(f,m)}return null}function y(f,d,m,S,N){if(typeof S=="string"&&S!==""||typeof S=="number")return f=f.get(m)||null,i(d,f,""+S,N);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case _r:return f=f.get(S.key===null?m:S.key)||null,a(d,f,S,N);case rn:return f=f.get(S.key===null?m:S.key)||null,c(d,f,S,N);case xt:var z=S._init;return y(f,d,m,z(S._payload),N)}if(Kn(S)||Fn(S))return f=f.get(m)||null,g(d,f,S,N,null);$r(d,S)}return null}function k(f,d,m,S){for(var N=null,z=null,C=d,D=d=0,A=null;C!==null&&D<m.length;D++){C.index>D?(A=C,C=null):A=C.sibling;var v=p(f,C,m[D],S);if(v===null){C===null&&(C=A);break}e&&C&&v.alternate===null&&t(f,C),d=l(v,d,D),z===null?N=v:z.sibling=v,z=v,C=A}if(D===m.length)return n(f,C),te&&Vt(f,D),N;if(C===null){for(;D<m.length;D++)C=h(f,m[D],S),C!==null&&(d=l(C,d,D),z===null?N=C:z.sibling=C,z=C);return te&&Vt(f,D),N}for(C=r(f,C);D<m.length;D++)A=y(C,f,D,m[D],S),A!==null&&(e&&A.alternate!==null&&C.delete(A.key===null?D:A.key),d=l(A,d,D),z===null?N=A:z.sibling=A,z=A);return e&&C.forEach(function(_){return t(f,_)}),te&&Vt(f,D),N}function w(f,d,m,S){var N=Fn(m);if(typeof N!="function")throw Error(P(150));if(m=N.call(m),m==null)throw Error(P(151));for(var z=N=null,C=d,D=d=0,A=null,v=m.next();C!==null&&!v.done;D++,v=m.next()){C.index>D?(A=C,C=null):A=C.sibling;var _=p(f,C,v.value,S);if(_===null){C===null&&(C=A);break}e&&C&&_.alternate===null&&t(f,C),d=l(_,d,D),z===null?N=_:z.sibling=_,z=_,C=A}if(v.done)return n(f,C),te&&Vt(f,D),N;if(C===null){for(;!v.done;D++,v=m.next())v=h(f,v.value,S),v!==null&&(d=l(v,d,D),z===null?N=v:z.sibling=v,z=v);return te&&Vt(f,D),N}for(C=r(f,C);!v.done;D++,v=m.next())v=y(C,f,D,v.value,S),v!==null&&(e&&v.alternate!==null&&C.delete(v.key===null?D:v.key),d=l(v,d,D),z===null?N=v:z.sibling=v,z=v);return e&&C.forEach(function(R){return t(f,R)}),te&&Vt(f,D),N}function E(f,d,m,S){if(typeof m=="object"&&m!==null&&m.type===on&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case _r:e:{for(var N=m.key,z=d;z!==null;){if(z.key===N){if(N=m.type,N===on){if(z.tag===7){n(f,z.sibling),d=o(z,m.props.children),d.return=f,f=d;break e}}else if(z.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===xt&&la(N)===z.type){n(f,z.sibling),d=o(z,m.props),d.ref=$n(f,z,m),d.return=f,f=d;break e}n(f,z);break}else t(f,z);z=z.sibling}m.type===on?(d=Wt(m.props.children,f.mode,S,m.key),d.return=f,f=d):(S=oo(m.type,m.key,m.props,null,f.mode,S),S.ref=$n(f,d,m),S.return=f,f=S)}return s(f);case rn:e:{for(z=m.key;d!==null;){if(d.key===z)if(d.tag===4&&d.stateNode.containerInfo===m.containerInfo&&d.stateNode.implementation===m.implementation){n(f,d.sibling),d=o(d,m.children||[]),d.return=f,f=d;break e}else{n(f,d);break}else t(f,d);d=d.sibling}d=El(m,f.mode,S),d.return=f,f=d}return s(f);case xt:return z=m._init,E(f,d,z(m._payload),S)}if(Kn(m))return k(f,d,m,S);if(Fn(m))return w(f,d,m,S);$r(f,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,d!==null&&d.tag===6?(n(f,d.sibling),d=o(d,m),d.return=f,f=d):(n(f,d),d=jl(m,f.mode,S),d.return=f,f=d),s(f)):n(f,d)}return E}var Pn=nc(!0),rc=nc(!1),xo=At(null),wo=null,pn=null,Xs=null;function Zs(){Xs=pn=wo=null}function Ys(e){var t=xo.current;ee(xo),e._currentValue=t}function ls(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Sn(e,t){wo=e,Xs=pn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ze=!0),e.firstContext=null)}function Ue(e){var t=e._currentValue;if(Xs!==e)if(e={context:e,memoizedValue:t,next:null},pn===null){if(wo===null)throw Error(P(308));pn=e,wo.dependencies={lanes:0,firstContext:e}}else pn=pn.next=e;return t}var Bt=null;function Js(e){Bt===null?Bt=[e]:Bt.push(e)}function oc(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Js(t)):(n.next=o.next,o.next=n),t.interleaved=n,pt(e,r)}function pt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var wt=!1;function qs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function lc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ct(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Dt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,Q&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,pt(e,n)}return o=r.interleaved,o===null?(t.next=t,Js(r)):(t.next=o.next,o.next=t),r.interleaved=t,pt(e,n)}function Jr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Os(e,n)}}function sa(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,l=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};l===null?o=l=s:l=l.next=s,n=n.next}while(n!==null);l===null?o=l=t:l=l.next=t}else o=l=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function So(e,t,n,r){var o=e.updateQueue;wt=!1;var l=o.firstBaseUpdate,s=o.lastBaseUpdate,i=o.shared.pending;if(i!==null){o.shared.pending=null;var a=i,c=a.next;a.next=null,s===null?l=c:s.next=c,s=a;var g=e.alternate;g!==null&&(g=g.updateQueue,i=g.lastBaseUpdate,i!==s&&(i===null?g.firstBaseUpdate=c:i.next=c,g.lastBaseUpdate=a))}if(l!==null){var h=o.baseState;s=0,g=c=a=null,i=l;do{var p=i.lane,y=i.eventTime;if((r&p)===p){g!==null&&(g=g.next={eventTime:y,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var k=e,w=i;switch(p=t,y=n,w.tag){case 1:if(k=w.payload,typeof k=="function"){h=k.call(y,h,p);break e}h=k;break e;case 3:k.flags=k.flags&-65537|128;case 0:if(k=w.payload,p=typeof k=="function"?k.call(y,h,p):k,p==null)break e;h=oe({},h,p);break e;case 2:wt=!0}}i.callback!==null&&i.lane!==0&&(e.flags|=64,p=o.effects,p===null?o.effects=[i]:p.push(i))}else y={eventTime:y,lane:p,tag:i.tag,payload:i.payload,callback:i.callback,next:null},g===null?(c=g=y,a=h):g=g.next=y,s|=p;if(i=i.next,i===null){if(i=o.shared.pending,i===null)break;p=i,i=p.next,p.next=null,o.lastBaseUpdate=p,o.shared.pending=null}}while(!0);if(g===null&&(a=h),o.baseState=a,o.firstBaseUpdate=c,o.lastBaseUpdate=g,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else l===null&&(o.shared.lanes=0);Xt|=s,e.lanes=s,e.memoizedState=h}}function ia(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(P(191,o));o.call(r)}}}var Er={},rt=At(Er),gr=At(Er),vr=At(Er);function Ut(e){if(e===Er)throw Error(P(174));return e}function ei(e,t){switch(Y(vr,t),Y(gr,e),Y(rt,Er),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Vl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Vl(t,e)}ee(rt),Y(rt,t)}function zn(){ee(rt),ee(gr),ee(vr)}function sc(e){Ut(vr.current);var t=Ut(rt.current),n=Vl(t,e.type);t!==n&&(Y(gr,e),Y(rt,n))}function ti(e){gr.current===e&&(ee(rt),ee(gr))}var ne=At(0);function ko(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var xl=[];function ni(){for(var e=0;e<xl.length;e++)xl[e]._workInProgressVersionPrimary=null;xl.length=0}var qr=ht.ReactCurrentDispatcher,wl=ht.ReactCurrentBatchConfig,Gt=0,re=null,ue=null,de=null,Co=!1,tr=!1,yr=0,Up=0;function ve(){throw Error(P(321))}function ri(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ye(e[n],t[n]))return!1;return!0}function oi(e,t,n,r,o,l){if(Gt=l,re=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,qr.current=e===null||e.memoizedState===null?Kp:Gp,e=n(r,o),tr){l=0;do{if(tr=!1,yr=0,25<=l)throw Error(P(301));l+=1,de=ue=null,t.updateQueue=null,qr.current=Xp,e=n(r,o)}while(tr)}if(qr.current=No,t=ue!==null&&ue.next!==null,Gt=0,de=ue=re=null,Co=!1,t)throw Error(P(300));return e}function li(){var e=yr!==0;return yr=0,e}function et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return de===null?re.memoizedState=de=e:de=de.next=e,de}function He(){if(ue===null){var e=re.alternate;e=e!==null?e.memoizedState:null}else e=ue.next;var t=de===null?re.memoizedState:de.next;if(t!==null)de=t,ue=e;else{if(e===null)throw Error(P(310));ue=e,e={memoizedState:ue.memoizedState,baseState:ue.baseState,baseQueue:ue.baseQueue,queue:ue.queue,next:null},de===null?re.memoizedState=de=e:de=de.next=e}return de}function xr(e,t){return typeof t=="function"?t(e):t}function Sl(e){var t=He(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=ue,o=r.baseQueue,l=n.pending;if(l!==null){if(o!==null){var s=o.next;o.next=l.next,l.next=s}r.baseQueue=o=l,n.pending=null}if(o!==null){l=o.next,r=r.baseState;var i=s=null,a=null,c=l;do{var g=c.lane;if((Gt&g)===g)a!==null&&(a=a.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var h={lane:g,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};a===null?(i=a=h,s=r):a=a.next=h,re.lanes|=g,Xt|=g}c=c.next}while(c!==null&&c!==l);a===null?s=r:a.next=i,Ye(r,t.memoizedState)||(ze=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do l=o.lane,re.lanes|=l,Xt|=l,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function kl(e){var t=He(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,l=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do l=e(l,s.action),s=s.next;while(s!==o);Ye(l,t.memoizedState)||(ze=!0),t.memoizedState=l,t.baseQueue===null&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function ic(){}function ac(e,t){var n=re,r=He(),o=t(),l=!Ye(r.memoizedState,o);if(l&&(r.memoizedState=o,ze=!0),r=r.queue,si(dc.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||de!==null&&de.memoizedState.tag&1){if(n.flags|=2048,wr(9,cc.bind(null,n,r,o,t),void 0,null),fe===null)throw Error(P(349));Gt&30||uc(n,t,o)}return o}function uc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=re.updateQueue,t===null?(t={lastEffect:null,stores:null},re.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function cc(e,t,n,r){t.value=n,t.getSnapshot=r,fc(t)&&pc(e)}function dc(e,t,n){return n(function(){fc(t)&&pc(e)})}function fc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ye(e,n)}catch{return!0}}function pc(e){var t=pt(e,1);t!==null&&Ze(t,e,1,-1)}function aa(e){var t=et();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xr,lastRenderedState:e},t.queue=e,e=e.dispatch=Qp.bind(null,re,e),[t.memoizedState,e]}function wr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=re.updateQueue,t===null?(t={lastEffect:null,stores:null},re.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function mc(){return He().memoizedState}function eo(e,t,n,r){var o=et();re.flags|=e,o.memoizedState=wr(1|t,n,void 0,r===void 0?null:r)}function Bo(e,t,n,r){var o=He();r=r===void 0?null:r;var l=void 0;if(ue!==null){var s=ue.memoizedState;if(l=s.destroy,r!==null&&ri(r,s.deps)){o.memoizedState=wr(t,n,l,r);return}}re.flags|=e,o.memoizedState=wr(1|t,n,l,r)}function ua(e,t){return eo(8390656,8,e,t)}function si(e,t){return Bo(2048,8,e,t)}function hc(e,t){return Bo(4,2,e,t)}function gc(e,t){return Bo(4,4,e,t)}function vc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function yc(e,t,n){return n=n!=null?n.concat([e]):null,Bo(4,4,vc.bind(null,t,e),n)}function ii(){}function xc(e,t){var n=He();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ri(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function wc(e,t){var n=He();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ri(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Sc(e,t,n){return Gt&21?(Ye(n,t)||(n=Eu(),re.lanes|=n,Xt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ze=!0),e.memoizedState=n)}function Hp(e,t){var n=K;K=n!==0&&4>n?n:4,e(!0);var r=wl.transition;wl.transition={};try{e(!1),t()}finally{K=n,wl.transition=r}}function kc(){return He().memoizedState}function Wp(e,t,n){var r=Mt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Cc(e))Nc(t,n);else if(n=oc(e,t,n,r),n!==null){var o=Ne();Ze(n,e,r,o),jc(n,t,r)}}function Qp(e,t,n){var r=Mt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Cc(e))Nc(t,o);else{var l=e.alternate;if(e.lanes===0&&(l===null||l.lanes===0)&&(l=t.lastRenderedReducer,l!==null))try{var s=t.lastRenderedState,i=l(s,n);if(o.hasEagerState=!0,o.eagerState=i,Ye(i,s)){var a=t.interleaved;a===null?(o.next=o,Js(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=oc(e,t,o,r),n!==null&&(o=Ne(),Ze(n,e,r,o),jc(n,t,r))}}function Cc(e){var t=e.alternate;return e===re||t!==null&&t===re}function Nc(e,t){tr=Co=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function jc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Os(e,n)}}var No={readContext:Ue,useCallback:ve,useContext:ve,useEffect:ve,useImperativeHandle:ve,useInsertionEffect:ve,useLayoutEffect:ve,useMemo:ve,useReducer:ve,useRef:ve,useState:ve,useDebugValue:ve,useDeferredValue:ve,useTransition:ve,useMutableSource:ve,useSyncExternalStore:ve,useId:ve,unstable_isNewReconciler:!1},Kp={readContext:Ue,useCallback:function(e,t){return et().memoizedState=[e,t===void 0?null:t],e},useContext:Ue,useEffect:ua,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,eo(4194308,4,vc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return eo(4194308,4,e,t)},useInsertionEffect:function(e,t){return eo(4,2,e,t)},useMemo:function(e,t){var n=et();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=et();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Wp.bind(null,re,e),[r.memoizedState,e]},useRef:function(e){var t=et();return e={current:e},t.memoizedState=e},useState:aa,useDebugValue:ii,useDeferredValue:function(e){return et().memoizedState=e},useTransition:function(){var e=aa(!1),t=e[0];return e=Hp.bind(null,e[1]),et().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=re,o=et();if(te){if(n===void 0)throw Error(P(407));n=n()}else{if(n=t(),fe===null)throw Error(P(349));Gt&30||uc(r,t,n)}o.memoizedState=n;var l={value:n,getSnapshot:t};return o.queue=l,ua(dc.bind(null,r,l,e),[e]),r.flags|=2048,wr(9,cc.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=et(),t=fe.identifierPrefix;if(te){var n=ut,r=at;n=(r&~(1<<32-Xe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=yr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Up++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Gp={readContext:Ue,useCallback:xc,useContext:Ue,useEffect:si,useImperativeHandle:yc,useInsertionEffect:hc,useLayoutEffect:gc,useMemo:wc,useReducer:Sl,useRef:mc,useState:function(){return Sl(xr)},useDebugValue:ii,useDeferredValue:function(e){var t=He();return Sc(t,ue.memoizedState,e)},useTransition:function(){var e=Sl(xr)[0],t=He().memoizedState;return[e,t]},useMutableSource:ic,useSyncExternalStore:ac,useId:kc,unstable_isNewReconciler:!1},Xp={readContext:Ue,useCallback:xc,useContext:Ue,useEffect:si,useImperativeHandle:yc,useInsertionEffect:hc,useLayoutEffect:gc,useMemo:wc,useReducer:kl,useRef:mc,useState:function(){return kl(xr)},useDebugValue:ii,useDeferredValue:function(e){var t=He();return ue===null?t.memoizedState=e:Sc(t,ue.memoizedState,e)},useTransition:function(){var e=kl(xr)[0],t=He().memoizedState;return[e,t]},useMutableSource:ic,useSyncExternalStore:ac,useId:kc,unstable_isNewReconciler:!1};function Qe(e,t){if(e&&e.defaultProps){t=oe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ss(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:oe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Uo={isMounted:function(e){return(e=e._reactInternals)?qt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ne(),o=Mt(e),l=ct(r,o);l.payload=t,n!=null&&(l.callback=n),t=Dt(e,l,o),t!==null&&(Ze(t,e,o,r),Jr(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ne(),o=Mt(e),l=ct(r,o);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=Dt(e,l,o),t!==null&&(Ze(t,e,o,r),Jr(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ne(),r=Mt(e),o=ct(n,r);o.tag=2,t!=null&&(o.callback=t),t=Dt(e,o,r),t!==null&&(Ze(t,e,r,n),Jr(t,e,r))}};function ca(e,t,n,r,o,l,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,l,s):t.prototype&&t.prototype.isPureReactComponent?!fr(n,r)||!fr(o,l):!0}function Ec(e,t,n){var r=!1,o=It,l=t.contextType;return typeof l=="object"&&l!==null?l=Ue(l):(o=_e(t)?Qt:Se.current,r=t.contextTypes,l=(r=r!=null)?jn(e,o):It),t=new t(n,l),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Uo,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=l),t}function da(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Uo.enqueueReplaceState(t,t.state,null)}function is(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},qs(e);var l=t.contextType;typeof l=="object"&&l!==null?o.context=Ue(l):(l=_e(t)?Qt:Se.current,o.context=jn(e,l)),o.state=e.memoizedState,l=t.getDerivedStateFromProps,typeof l=="function"&&(ss(e,t,l,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Uo.enqueueReplaceState(o,o.state,null),So(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Dn(e,t){try{var n="",r=t;do n+=Cf(r),r=r.return;while(r);var o=n}catch(l){o=`
Error generating stack: `+l.message+`
`+l.stack}return{value:e,source:t,stack:o,digest:null}}function Cl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function as(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Zp=typeof WeakMap=="function"?WeakMap:Map;function Pc(e,t,n){n=ct(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Eo||(Eo=!0,ys=r),as(e,t)},n}function zc(e,t,n){n=ct(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){as(e,t)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(n.callback=function(){as(e,t),typeof r!="function"&&(_t===null?_t=new Set([this]):_t.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function fa(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Zp;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=cm.bind(null,e,t,n),t.then(e,e))}function pa(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ma(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ct(-1,1),t.tag=2,Dt(n,t,1))),n.lanes|=1),e)}var Yp=ht.ReactCurrentOwner,ze=!1;function Ce(e,t,n,r){t.child=e===null?rc(t,null,n,r):Pn(t,e.child,n,r)}function ha(e,t,n,r,o){n=n.render;var l=t.ref;return Sn(t,o),r=oi(e,t,n,r,l,o),n=li(),e!==null&&!ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,mt(e,t,o)):(te&&n&&Qs(t),t.flags|=1,Ce(e,t,r,o),t.child)}function ga(e,t,n,r,o){if(e===null){var l=n.type;return typeof l=="function"&&!hi(l)&&l.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=l,Dc(e,t,l,r,o)):(e=oo(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(l=e.child,!(e.lanes&o)){var s=l.memoizedProps;if(n=n.compare,n=n!==null?n:fr,n(s,r)&&e.ref===t.ref)return mt(e,t,o)}return t.flags|=1,e=Lt(l,r),e.ref=t.ref,e.return=t,t.child=e}function Dc(e,t,n,r,o){if(e!==null){var l=e.memoizedProps;if(fr(l,r)&&e.ref===t.ref)if(ze=!1,t.pendingProps=r=l,(e.lanes&o)!==0)e.flags&131072&&(ze=!0);else return t.lanes=e.lanes,mt(e,t,o)}return us(e,t,n,r,o)}function _c(e,t,n){var r=t.pendingProps,o=r.children,l=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Y(hn,Le),Le|=n;else{if(!(n&1073741824))return e=l!==null?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Y(hn,Le),Le|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=l!==null?l.baseLanes:n,Y(hn,Le),Le|=r}else l!==null?(r=l.baseLanes|n,t.memoizedState=null):r=n,Y(hn,Le),Le|=r;return Ce(e,t,o,n),t.child}function Mc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function us(e,t,n,r,o){var l=_e(n)?Qt:Se.current;return l=jn(t,l),Sn(t,o),n=oi(e,t,n,r,l,o),r=li(),e!==null&&!ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,mt(e,t,o)):(te&&r&&Qs(t),t.flags|=1,Ce(e,t,n,o),t.child)}function va(e,t,n,r,o){if(_e(n)){var l=!0;go(t)}else l=!1;if(Sn(t,o),t.stateNode===null)to(e,t),Ec(t,n,r),is(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,i=t.memoizedProps;s.props=i;var a=s.context,c=n.contextType;typeof c=="object"&&c!==null?c=Ue(c):(c=_e(n)?Qt:Se.current,c=jn(t,c));var g=n.getDerivedStateFromProps,h=typeof g=="function"||typeof s.getSnapshotBeforeUpdate=="function";h||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(i!==r||a!==c)&&da(t,s,r,c),wt=!1;var p=t.memoizedState;s.state=p,So(t,r,s,o),a=t.memoizedState,i!==r||p!==a||De.current||wt?(typeof g=="function"&&(ss(t,n,g,r),a=t.memoizedState),(i=wt||ca(t,n,i,r,p,a,c))?(h||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=c,r=i):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,lc(e,t),i=t.memoizedProps,c=t.type===t.elementType?i:Qe(t.type,i),s.props=c,h=t.pendingProps,p=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=Ue(a):(a=_e(n)?Qt:Se.current,a=jn(t,a));var y=n.getDerivedStateFromProps;(g=typeof y=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(i!==h||p!==a)&&da(t,s,r,a),wt=!1,p=t.memoizedState,s.state=p,So(t,r,s,o);var k=t.memoizedState;i!==h||p!==k||De.current||wt?(typeof y=="function"&&(ss(t,n,y,r),k=t.memoizedState),(c=wt||ca(t,n,c,r,p,k,a)||!1)?(g||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,k,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,k,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=k),s.props=r,s.state=k,s.context=a,r=c):(typeof s.componentDidUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return cs(e,t,n,r,l,o)}function cs(e,t,n,r,o,l){Mc(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&na(t,n,!1),mt(e,t,l);r=t.stateNode,Yp.current=t;var i=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Pn(t,e.child,null,l),t.child=Pn(t,null,i,l)):Ce(e,t,i,l),t.memoizedState=r.state,o&&na(t,n,!0),t.child}function Lc(e){var t=e.stateNode;t.pendingContext?ta(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ta(e,t.context,!1),ei(e,t.containerInfo)}function ya(e,t,n,r,o){return En(),Gs(o),t.flags|=256,Ce(e,t,n,r),t.child}var ds={dehydrated:null,treeContext:null,retryLane:0};function fs(e){return{baseLanes:e,cachePool:null,transitions:null}}function Tc(e,t,n){var r=t.pendingProps,o=ne.current,l=!1,s=(t.flags&128)!==0,i;if((i=s)||(i=e!==null&&e.memoizedState===null?!1:(o&2)!==0),i?(l=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Y(ne,o&1),e===null)return os(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,l?(r=t.mode,l=t.child,s={mode:"hidden",children:s},!(r&1)&&l!==null?(l.childLanes=0,l.pendingProps=s):l=Qo(s,r,0,null),e=Wt(e,r,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=fs(n),t.memoizedState=ds,e):ai(t,s));if(o=e.memoizedState,o!==null&&(i=o.dehydrated,i!==null))return Jp(e,t,s,r,i,o,n);if(l){l=r.fallback,s=t.mode,o=e.child,i=o.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Lt(o,a),r.subtreeFlags=o.subtreeFlags&14680064),i!==null?l=Lt(i,l):(l=Wt(l,s,n,null),l.flags|=2),l.return=t,r.return=t,r.sibling=l,t.child=r,r=l,l=t.child,s=e.child.memoizedState,s=s===null?fs(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=ds,r}return l=e.child,e=l.sibling,r=Lt(l,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ai(e,t){return t=Qo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Br(e,t,n,r){return r!==null&&Gs(r),Pn(t,e.child,null,n),e=ai(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Jp(e,t,n,r,o,l,s){if(n)return t.flags&256?(t.flags&=-257,r=Cl(Error(P(422))),Br(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(l=r.fallback,o=t.mode,r=Qo({mode:"visible",children:r.children},o,0,null),l=Wt(l,o,s,null),l.flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,t.mode&1&&Pn(t,e.child,null,s),t.child.memoizedState=fs(s),t.memoizedState=ds,l);if(!(t.mode&1))return Br(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var i=r.dgst;return r=i,l=Error(P(419)),r=Cl(l,r,void 0),Br(e,t,s,r)}if(i=(s&e.childLanes)!==0,ze||i){if(r=fe,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==l.retryLane&&(l.retryLane=o,pt(e,o),Ze(r,e,o,-1))}return mi(),r=Cl(Error(P(421))),Br(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=dm.bind(null,e),o._reactRetry=t,null):(e=l.treeContext,Te=zt(o.nextSibling),Ie=t,te=!0,Ge=null,e!==null&&(Ve[be++]=at,Ve[be++]=ut,Ve[be++]=Kt,at=e.id,ut=e.overflow,Kt=t),t=ai(t,r.children),t.flags|=4096,t)}function xa(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ls(e.return,t,n)}function Nl(e,t,n,r,o){var l=e.memoizedState;l===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=o)}function Ic(e,t,n){var r=t.pendingProps,o=r.revealOrder,l=r.tail;if(Ce(e,t,r.children,n),r=ne.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&xa(e,n,t);else if(e.tag===19)xa(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Y(ne,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&ko(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Nl(t,!1,o,n,l);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&ko(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Nl(t,!0,n,null,l);break;case"together":Nl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function to(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function mt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Xt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(P(153));if(t.child!==null){for(e=t.child,n=Lt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Lt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function qp(e,t,n){switch(t.tag){case 3:Lc(t),En();break;case 5:sc(t);break;case 1:_e(t.type)&&go(t);break;case 4:ei(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Y(xo,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Y(ne,ne.current&1),t.flags|=128,null):n&t.child.childLanes?Tc(e,t,n):(Y(ne,ne.current&1),e=mt(e,t,n),e!==null?e.sibling:null);Y(ne,ne.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Ic(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Y(ne,ne.current),r)break;return null;case 22:case 23:return t.lanes=0,_c(e,t,n)}return mt(e,t,n)}var Rc,ps,Fc,Ac;Rc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ps=function(){};Fc=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Ut(rt.current);var l=null;switch(n){case"input":o=Rl(e,o),r=Rl(e,r),l=[];break;case"select":o=oe({},o,{value:void 0}),r=oe({},r,{value:void 0}),l=[];break;case"textarea":o=Ol(e,o),r=Ol(e,r),l=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=mo)}bl(n,r);var s;n=null;for(c in o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&o[c]!=null)if(c==="style"){var i=o[c];for(s in i)i.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(lr.hasOwnProperty(c)?l||(l=[]):(l=l||[]).push(c,null));for(c in r){var a=r[c];if(i=o!=null?o[c]:void 0,r.hasOwnProperty(c)&&a!==i&&(a!=null||i!=null))if(c==="style")if(i){for(s in i)!i.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&i[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(l||(l=[]),l.push(c,n)),n=a;else c==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,i=i?i.__html:void 0,a!=null&&i!==a&&(l=l||[]).push(c,a)):c==="children"?typeof a!="string"&&typeof a!="number"||(l=l||[]).push(c,""+a):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(lr.hasOwnProperty(c)?(a!=null&&c==="onScroll"&&q("scroll",e),l||i===a||(l=[])):(l=l||[]).push(c,a))}n&&(l=l||[]).push("style",n);var c=l;(t.updateQueue=c)&&(t.flags|=4)}};Ac=function(e,t,n,r){n!==r&&(t.flags|=4)};function Bn(e,t){if(!te)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ye(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function em(e,t,n){var r=t.pendingProps;switch(Ks(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ye(t),null;case 1:return _e(t.type)&&ho(),ye(t),null;case 3:return r=t.stateNode,zn(),ee(De),ee(Se),ni(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(br(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ge!==null&&(Ss(Ge),Ge=null))),ps(e,t),ye(t),null;case 5:ti(t);var o=Ut(vr.current);if(n=t.type,e!==null&&t.stateNode!=null)Fc(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(P(166));return ye(t),null}if(e=Ut(rt.current),br(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[tt]=t,r[hr]=l,e=(t.mode&1)!==0,n){case"dialog":q("cancel",r),q("close",r);break;case"iframe":case"object":case"embed":q("load",r);break;case"video":case"audio":for(o=0;o<Xn.length;o++)q(Xn[o],r);break;case"source":q("error",r);break;case"img":case"image":case"link":q("error",r),q("load",r);break;case"details":q("toggle",r);break;case"input":zi(r,l),q("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},q("invalid",r);break;case"textarea":_i(r,l),q("invalid",r)}bl(n,l),o=null;for(var s in l)if(l.hasOwnProperty(s)){var i=l[s];s==="children"?typeof i=="string"?r.textContent!==i&&(l.suppressHydrationWarning!==!0&&Vr(r.textContent,i,e),o=["children",i]):typeof i=="number"&&r.textContent!==""+i&&(l.suppressHydrationWarning!==!0&&Vr(r.textContent,i,e),o=["children",""+i]):lr.hasOwnProperty(s)&&i!=null&&s==="onScroll"&&q("scroll",r)}switch(n){case"input":Mr(r),Di(r,l,!0);break;case"textarea":Mr(r),Mi(r);break;case"select":case"option":break;default:typeof l.onClick=="function"&&(r.onclick=mo)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=du(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[tt]=t,e[hr]=r,Rc(e,t,!1,!1),t.stateNode=e;e:{switch(s=$l(n,r),n){case"dialog":q("cancel",e),q("close",e),o=r;break;case"iframe":case"object":case"embed":q("load",e),o=r;break;case"video":case"audio":for(o=0;o<Xn.length;o++)q(Xn[o],e);o=r;break;case"source":q("error",e),o=r;break;case"img":case"image":case"link":q("error",e),q("load",e),o=r;break;case"details":q("toggle",e),o=r;break;case"input":zi(e,r),o=Rl(e,r),q("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=oe({},r,{value:void 0}),q("invalid",e);break;case"textarea":_i(e,r),o=Ol(e,r),q("invalid",e);break;default:o=r}bl(n,o),i=o;for(l in i)if(i.hasOwnProperty(l)){var a=i[l];l==="style"?mu(e,a):l==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&fu(e,a)):l==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&sr(e,a):typeof a=="number"&&sr(e,""+a):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&(lr.hasOwnProperty(l)?a!=null&&l==="onScroll"&&q("scroll",e):a!=null&&Ls(e,l,a,s))}switch(n){case"input":Mr(e),Di(e,r,!1);break;case"textarea":Mr(e),Mi(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Tt(r.value));break;case"select":e.multiple=!!r.multiple,l=r.value,l!=null?vn(e,!!r.multiple,l,!1):r.defaultValue!=null&&vn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=mo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ye(t),null;case 6:if(e&&t.stateNode!=null)Ac(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(P(166));if(n=Ut(vr.current),Ut(rt.current),br(t)){if(r=t.stateNode,n=t.memoizedProps,r[tt]=t,(l=r.nodeValue!==n)&&(e=Ie,e!==null))switch(e.tag){case 3:Vr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Vr(r.nodeValue,n,(e.mode&1)!==0)}l&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[tt]=t,t.stateNode=r}return ye(t),null;case 13:if(ee(ne),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(te&&Te!==null&&t.mode&1&&!(t.flags&128))tc(),En(),t.flags|=98560,l=!1;else if(l=br(t),r!==null&&r.dehydrated!==null){if(e===null){if(!l)throw Error(P(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(P(317));l[tt]=t}else En(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ye(t),l=!1}else Ge!==null&&(Ss(Ge),Ge=null),l=!0;if(!l)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ne.current&1?ce===0&&(ce=3):mi())),t.updateQueue!==null&&(t.flags|=4),ye(t),null);case 4:return zn(),ps(e,t),e===null&&pr(t.stateNode.containerInfo),ye(t),null;case 10:return Ys(t.type._context),ye(t),null;case 17:return _e(t.type)&&ho(),ye(t),null;case 19:if(ee(ne),l=t.memoizedState,l===null)return ye(t),null;if(r=(t.flags&128)!==0,s=l.rendering,s===null)if(r)Bn(l,!1);else{if(ce!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=ko(e),s!==null){for(t.flags|=128,Bn(l,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)l=n,e=r,l.flags&=14680066,s=l.alternate,s===null?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=s.childLanes,l.lanes=s.lanes,l.child=s.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=s.memoizedProps,l.memoizedState=s.memoizedState,l.updateQueue=s.updateQueue,l.type=s.type,e=s.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Y(ne,ne.current&1|2),t.child}e=e.sibling}l.tail!==null&&ie()>_n&&(t.flags|=128,r=!0,Bn(l,!1),t.lanes=4194304)}else{if(!r)if(e=ko(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Bn(l,!0),l.tail===null&&l.tailMode==="hidden"&&!s.alternate&&!te)return ye(t),null}else 2*ie()-l.renderingStartTime>_n&&n!==1073741824&&(t.flags|=128,r=!0,Bn(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(n=l.last,n!==null?n.sibling=s:t.child=s,l.last=s)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=ie(),t.sibling=null,n=ne.current,Y(ne,r?n&1|2:n&1),t):(ye(t),null);case 22:case 23:return pi(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Le&1073741824&&(ye(t),t.subtreeFlags&6&&(t.flags|=8192)):ye(t),null;case 24:return null;case 25:return null}throw Error(P(156,t.tag))}function tm(e,t){switch(Ks(t),t.tag){case 1:return _e(t.type)&&ho(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return zn(),ee(De),ee(Se),ni(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ti(t),null;case 13:if(ee(ne),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(P(340));En()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ee(ne),null;case 4:return zn(),null;case 10:return Ys(t.type._context),null;case 22:case 23:return pi(),null;case 24:return null;default:return null}}var Ur=!1,we=!1,nm=typeof WeakSet=="function"?WeakSet:Set,M=null;function mn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){se(e,t,r)}else n.current=null}function ms(e,t,n){try{n()}catch(r){se(e,t,r)}}var wa=!1;function rm(e,t){if(Yl=co,e=Bu(),Ws(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch{n=null;break e}var s=0,i=-1,a=-1,c=0,g=0,h=e,p=null;t:for(;;){for(var y;h!==n||o!==0&&h.nodeType!==3||(i=s+o),h!==l||r!==0&&h.nodeType!==3||(a=s+r),h.nodeType===3&&(s+=h.nodeValue.length),(y=h.firstChild)!==null;)p=h,h=y;for(;;){if(h===e)break t;if(p===n&&++c===o&&(i=s),p===l&&++g===r&&(a=s),(y=h.nextSibling)!==null)break;h=p,p=h.parentNode}h=y}n=i===-1||a===-1?null:{start:i,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Jl={focusedElem:e,selectionRange:n},co=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var k=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(k!==null){var w=k.memoizedProps,E=k.memoizedState,f=t.stateNode,d=f.getSnapshotBeforeUpdate(t.elementType===t.type?w:Qe(t.type,w),E);f.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(S){se(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return k=wa,wa=!1,k}function nr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var l=o.destroy;o.destroy=void 0,l!==void 0&&ms(t,n,l)}o=o.next}while(o!==r)}}function Ho(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function hs(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Oc(e){var t=e.alternate;t!==null&&(e.alternate=null,Oc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[tt],delete t[hr],delete t[ts],delete t[Vp],delete t[bp])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Vc(e){return e.tag===5||e.tag===3||e.tag===4}function Sa(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Vc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function gs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=mo));else if(r!==4&&(e=e.child,e!==null))for(gs(e,t,n),e=e.sibling;e!==null;)gs(e,t,n),e=e.sibling}function vs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(vs(e,t,n),e=e.sibling;e!==null;)vs(e,t,n),e=e.sibling}var pe=null,Ke=!1;function gt(e,t,n){for(n=n.child;n!==null;)bc(e,t,n),n=n.sibling}function bc(e,t,n){if(nt&&typeof nt.onCommitFiberUnmount=="function")try{nt.onCommitFiberUnmount(Fo,n)}catch{}switch(n.tag){case 5:we||mn(n,t);case 6:var r=pe,o=Ke;pe=null,gt(e,t,n),pe=r,Ke=o,pe!==null&&(Ke?(e=pe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):pe.removeChild(n.stateNode));break;case 18:pe!==null&&(Ke?(e=pe,n=n.stateNode,e.nodeType===8?vl(e.parentNode,n):e.nodeType===1&&vl(e,n),cr(e)):vl(pe,n.stateNode));break;case 4:r=pe,o=Ke,pe=n.stateNode.containerInfo,Ke=!0,gt(e,t,n),pe=r,Ke=o;break;case 0:case 11:case 14:case 15:if(!we&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var l=o,s=l.destroy;l=l.tag,s!==void 0&&(l&2||l&4)&&ms(n,t,s),o=o.next}while(o!==r)}gt(e,t,n);break;case 1:if(!we&&(mn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){se(n,t,i)}gt(e,t,n);break;case 21:gt(e,t,n);break;case 22:n.mode&1?(we=(r=we)||n.memoizedState!==null,gt(e,t,n),we=r):gt(e,t,n);break;default:gt(e,t,n)}}function ka(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new nm),t.forEach(function(r){var o=fm.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function We(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var l=e,s=t,i=s;e:for(;i!==null;){switch(i.tag){case 5:pe=i.stateNode,Ke=!1;break e;case 3:pe=i.stateNode.containerInfo,Ke=!0;break e;case 4:pe=i.stateNode.containerInfo,Ke=!0;break e}i=i.return}if(pe===null)throw Error(P(160));bc(l,s,o),pe=null,Ke=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(c){se(o,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)$c(t,e),t=t.sibling}function $c(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(We(t,e),qe(e),r&4){try{nr(3,e,e.return),Ho(3,e)}catch(w){se(e,e.return,w)}try{nr(5,e,e.return)}catch(w){se(e,e.return,w)}}break;case 1:We(t,e),qe(e),r&512&&n!==null&&mn(n,n.return);break;case 5:if(We(t,e),qe(e),r&512&&n!==null&&mn(n,n.return),e.flags&32){var o=e.stateNode;try{sr(o,"")}catch(w){se(e,e.return,w)}}if(r&4&&(o=e.stateNode,o!=null)){var l=e.memoizedProps,s=n!==null?n.memoizedProps:l,i=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{i==="input"&&l.type==="radio"&&l.name!=null&&uu(o,l),$l(i,s);var c=$l(i,l);for(s=0;s<a.length;s+=2){var g=a[s],h=a[s+1];g==="style"?mu(o,h):g==="dangerouslySetInnerHTML"?fu(o,h):g==="children"?sr(o,h):Ls(o,g,h,c)}switch(i){case"input":Fl(o,l);break;case"textarea":cu(o,l);break;case"select":var p=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!l.multiple;var y=l.value;y!=null?vn(o,!!l.multiple,y,!1):p!==!!l.multiple&&(l.defaultValue!=null?vn(o,!!l.multiple,l.defaultValue,!0):vn(o,!!l.multiple,l.multiple?[]:"",!1))}o[hr]=l}catch(w){se(e,e.return,w)}}break;case 6:if(We(t,e),qe(e),r&4){if(e.stateNode===null)throw Error(P(162));o=e.stateNode,l=e.memoizedProps;try{o.nodeValue=l}catch(w){se(e,e.return,w)}}break;case 3:if(We(t,e),qe(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{cr(t.containerInfo)}catch(w){se(e,e.return,w)}break;case 4:We(t,e),qe(e);break;case 13:We(t,e),qe(e),o=e.child,o.flags&8192&&(l=o.memoizedState!==null,o.stateNode.isHidden=l,!l||o.alternate!==null&&o.alternate.memoizedState!==null||(di=ie())),r&4&&ka(e);break;case 22:if(g=n!==null&&n.memoizedState!==null,e.mode&1?(we=(c=we)||g,We(t,e),we=c):We(t,e),qe(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!g&&e.mode&1)for(M=e,g=e.child;g!==null;){for(h=M=g;M!==null;){switch(p=M,y=p.child,p.tag){case 0:case 11:case 14:case 15:nr(4,p,p.return);break;case 1:mn(p,p.return);var k=p.stateNode;if(typeof k.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,k.props=t.memoizedProps,k.state=t.memoizedState,k.componentWillUnmount()}catch(w){se(r,n,w)}}break;case 5:mn(p,p.return);break;case 22:if(p.memoizedState!==null){Na(h);continue}}y!==null?(y.return=p,M=y):Na(h)}g=g.sibling}e:for(g=null,h=e;;){if(h.tag===5){if(g===null){g=h;try{o=h.stateNode,c?(l=o.style,typeof l.setProperty=="function"?l.setProperty("display","none","important"):l.display="none"):(i=h.stateNode,a=h.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,i.style.display=pu("display",s))}catch(w){se(e,e.return,w)}}}else if(h.tag===6){if(g===null)try{h.stateNode.nodeValue=c?"":h.memoizedProps}catch(w){se(e,e.return,w)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;g===h&&(g=null),h=h.return}g===h&&(g=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:We(t,e),qe(e),r&4&&ka(e);break;case 21:break;default:We(t,e),qe(e)}}function qe(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Vc(n)){var r=n;break e}n=n.return}throw Error(P(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(sr(o,""),r.flags&=-33);var l=Sa(e);vs(e,l,o);break;case 3:case 4:var s=r.stateNode.containerInfo,i=Sa(e);gs(e,i,s);break;default:throw Error(P(161))}}catch(a){se(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function om(e,t,n){M=e,Bc(e)}function Bc(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var o=M,l=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||Ur;if(!s){var i=o.alternate,a=i!==null&&i.memoizedState!==null||we;i=Ur;var c=we;if(Ur=s,(we=a)&&!c)for(M=o;M!==null;)s=M,a=s.child,s.tag===22&&s.memoizedState!==null?ja(o):a!==null?(a.return=s,M=a):ja(o);for(;l!==null;)M=l,Bc(l),l=l.sibling;M=o,Ur=i,we=c}Ca(e)}else o.subtreeFlags&8772&&l!==null?(l.return=o,M=l):Ca(e)}}function Ca(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:we||Ho(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!we)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Qe(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;l!==null&&ia(t,l,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ia(t,s,n)}break;case 5:var i=t.stateNode;if(n===null&&t.flags&4){n=i;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var g=c.memoizedState;if(g!==null){var h=g.dehydrated;h!==null&&cr(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}we||t.flags&512&&hs(t)}catch(p){se(t,t.return,p)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Na(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function ja(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ho(4,t)}catch(a){se(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){se(t,o,a)}}var l=t.return;try{hs(t)}catch(a){se(t,l,a)}break;case 5:var s=t.return;try{hs(t)}catch(a){se(t,s,a)}}}catch(a){se(t,t.return,a)}if(t===e){M=null;break}var i=t.sibling;if(i!==null){i.return=t.return,M=i;break}M=t.return}}var lm=Math.ceil,jo=ht.ReactCurrentDispatcher,ui=ht.ReactCurrentOwner,Be=ht.ReactCurrentBatchConfig,Q=0,fe=null,ae=null,me=0,Le=0,hn=At(0),ce=0,Sr=null,Xt=0,Wo=0,ci=0,rr=null,Pe=null,di=0,_n=1/0,st=null,Eo=!1,ys=null,_t=null,Hr=!1,Nt=null,Po=0,or=0,xs=null,no=-1,ro=0;function Ne(){return Q&6?ie():no!==-1?no:no=ie()}function Mt(e){return e.mode&1?Q&2&&me!==0?me&-me:Bp.transition!==null?(ro===0&&(ro=Eu()),ro):(e=K,e!==0||(e=window.event,e=e===void 0?16:Tu(e.type)),e):1}function Ze(e,t,n,r){if(50<or)throw or=0,xs=null,Error(P(185));Cr(e,n,r),(!(Q&2)||e!==fe)&&(e===fe&&(!(Q&2)&&(Wo|=n),ce===4&&kt(e,me)),Me(e,r),n===1&&Q===0&&!(t.mode&1)&&(_n=ie()+500,$o&&Ot()))}function Me(e,t){var n=e.callbackNode;Bf(e,t);var r=uo(e,e===fe?me:0);if(r===0)n!==null&&Ii(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ii(n),t===1)e.tag===0?$p(Ea.bind(null,e)):Ju(Ea.bind(null,e)),Ap(function(){!(Q&6)&&Ot()}),n=null;else{switch(Pu(r)){case 1:n=As;break;case 4:n=Nu;break;case 16:n=ao;break;case 536870912:n=ju;break;default:n=ao}n=Zc(n,Uc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Uc(e,t){if(no=-1,ro=0,Q&6)throw Error(P(327));var n=e.callbackNode;if(kn()&&e.callbackNode!==n)return null;var r=uo(e,e===fe?me:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=zo(e,r);else{t=r;var o=Q;Q|=2;var l=Wc();(fe!==e||me!==t)&&(st=null,_n=ie()+500,Ht(e,t));do try{am();break}catch(i){Hc(e,i)}while(!0);Zs(),jo.current=l,Q=o,ae!==null?t=0:(fe=null,me=0,t=ce)}if(t!==0){if(t===2&&(o=Ql(e),o!==0&&(r=o,t=ws(e,o))),t===1)throw n=Sr,Ht(e,0),kt(e,r),Me(e,ie()),n;if(t===6)kt(e,r);else{if(o=e.current.alternate,!(r&30)&&!sm(o)&&(t=zo(e,r),t===2&&(l=Ql(e),l!==0&&(r=l,t=ws(e,l))),t===1))throw n=Sr,Ht(e,0),kt(e,r),Me(e,ie()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(P(345));case 2:bt(e,Pe,st);break;case 3:if(kt(e,r),(r&130023424)===r&&(t=di+500-ie(),10<t)){if(uo(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ne(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=es(bt.bind(null,e,Pe,st),t);break}bt(e,Pe,st);break;case 4:if(kt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-Xe(r);l=1<<s,s=t[s],s>o&&(o=s),r&=~l}if(r=o,r=ie()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*lm(r/1960))-r,10<r){e.timeoutHandle=es(bt.bind(null,e,Pe,st),r);break}bt(e,Pe,st);break;case 5:bt(e,Pe,st);break;default:throw Error(P(329))}}}return Me(e,ie()),e.callbackNode===n?Uc.bind(null,e):null}function ws(e,t){var n=rr;return e.current.memoizedState.isDehydrated&&(Ht(e,t).flags|=256),e=zo(e,t),e!==2&&(t=Pe,Pe=n,t!==null&&Ss(t)),e}function Ss(e){Pe===null?Pe=e:Pe.push.apply(Pe,e)}function sm(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],l=o.getSnapshot;o=o.value;try{if(!Ye(l(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function kt(e,t){for(t&=~ci,t&=~Wo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Xe(t),r=1<<n;e[n]=-1,t&=~r}}function Ea(e){if(Q&6)throw Error(P(327));kn();var t=uo(e,0);if(!(t&1))return Me(e,ie()),null;var n=zo(e,t);if(e.tag!==0&&n===2){var r=Ql(e);r!==0&&(t=r,n=ws(e,r))}if(n===1)throw n=Sr,Ht(e,0),kt(e,t),Me(e,ie()),n;if(n===6)throw Error(P(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,bt(e,Pe,st),Me(e,ie()),null}function fi(e,t){var n=Q;Q|=1;try{return e(t)}finally{Q=n,Q===0&&(_n=ie()+500,$o&&Ot())}}function Zt(e){Nt!==null&&Nt.tag===0&&!(Q&6)&&kn();var t=Q;Q|=1;var n=Be.transition,r=K;try{if(Be.transition=null,K=1,e)return e()}finally{K=r,Be.transition=n,Q=t,!(Q&6)&&Ot()}}function pi(){Le=hn.current,ee(hn)}function Ht(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Fp(n)),ae!==null)for(n=ae.return;n!==null;){var r=n;switch(Ks(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ho();break;case 3:zn(),ee(De),ee(Se),ni();break;case 5:ti(r);break;case 4:zn();break;case 13:ee(ne);break;case 19:ee(ne);break;case 10:Ys(r.type._context);break;case 22:case 23:pi()}n=n.return}if(fe=e,ae=e=Lt(e.current,null),me=Le=t,ce=0,Sr=null,ci=Wo=Xt=0,Pe=rr=null,Bt!==null){for(t=0;t<Bt.length;t++)if(n=Bt[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,l=n.pending;if(l!==null){var s=l.next;l.next=o,r.next=s}n.pending=r}Bt=null}return e}function Hc(e,t){do{var n=ae;try{if(Zs(),qr.current=No,Co){for(var r=re.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Co=!1}if(Gt=0,de=ue=re=null,tr=!1,yr=0,ui.current=null,n===null||n.return===null){ce=1,Sr=t,ae=null;break}e:{var l=e,s=n.return,i=n,a=t;if(t=me,i.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var c=a,g=i,h=g.tag;if(!(g.mode&1)&&(h===0||h===11||h===15)){var p=g.alternate;p?(g.updateQueue=p.updateQueue,g.memoizedState=p.memoizedState,g.lanes=p.lanes):(g.updateQueue=null,g.memoizedState=null)}var y=pa(s);if(y!==null){y.flags&=-257,ma(y,s,i,l,t),y.mode&1&&fa(l,c,t),t=y,a=c;var k=t.updateQueue;if(k===null){var w=new Set;w.add(a),t.updateQueue=w}else k.add(a);break e}else{if(!(t&1)){fa(l,c,t),mi();break e}a=Error(P(426))}}else if(te&&i.mode&1){var E=pa(s);if(E!==null){!(E.flags&65536)&&(E.flags|=256),ma(E,s,i,l,t),Gs(Dn(a,i));break e}}l=a=Dn(a,i),ce!==4&&(ce=2),rr===null?rr=[l]:rr.push(l),l=s;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t;var f=Pc(l,a,t);sa(l,f);break e;case 1:i=a;var d=l.type,m=l.stateNode;if(!(l.flags&128)&&(typeof d.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(_t===null||!_t.has(m)))){l.flags|=65536,t&=-t,l.lanes|=t;var S=zc(l,i,t);sa(l,S);break e}}l=l.return}while(l!==null)}Kc(n)}catch(N){t=N,ae===n&&n!==null&&(ae=n=n.return);continue}break}while(!0)}function Wc(){var e=jo.current;return jo.current=No,e===null?No:e}function mi(){(ce===0||ce===3||ce===2)&&(ce=4),fe===null||!(Xt&268435455)&&!(Wo&268435455)||kt(fe,me)}function zo(e,t){var n=Q;Q|=2;var r=Wc();(fe!==e||me!==t)&&(st=null,Ht(e,t));do try{im();break}catch(o){Hc(e,o)}while(!0);if(Zs(),Q=n,jo.current=r,ae!==null)throw Error(P(261));return fe=null,me=0,ce}function im(){for(;ae!==null;)Qc(ae)}function am(){for(;ae!==null&&!Tf();)Qc(ae)}function Qc(e){var t=Xc(e.alternate,e,Le);e.memoizedProps=e.pendingProps,t===null?Kc(e):ae=t,ui.current=null}function Kc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=tm(n,t),n!==null){n.flags&=32767,ae=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ce=6,ae=null;return}}else if(n=em(n,t,Le),n!==null){ae=n;return}if(t=t.sibling,t!==null){ae=t;return}ae=t=e}while(t!==null);ce===0&&(ce=5)}function bt(e,t,n){var r=K,o=Be.transition;try{Be.transition=null,K=1,um(e,t,n,r)}finally{Be.transition=o,K=r}return null}function um(e,t,n,r){do kn();while(Nt!==null);if(Q&6)throw Error(P(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(P(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(Uf(e,l),e===fe&&(ae=fe=null,me=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Hr||(Hr=!0,Zc(ao,function(){return kn(),null})),l=(n.flags&15990)!==0,n.subtreeFlags&15990||l){l=Be.transition,Be.transition=null;var s=K;K=1;var i=Q;Q|=4,ui.current=null,rm(e,n),$c(n,e),Dp(Jl),co=!!Yl,Jl=Yl=null,e.current=n,om(n),If(),Q=i,K=s,Be.transition=l}else e.current=n;if(Hr&&(Hr=!1,Nt=e,Po=o),l=e.pendingLanes,l===0&&(_t=null),Af(n.stateNode),Me(e,ie()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Eo)throw Eo=!1,e=ys,ys=null,e;return Po&1&&e.tag!==0&&kn(),l=e.pendingLanes,l&1?e===xs?or++:(or=0,xs=e):or=0,Ot(),null}function kn(){if(Nt!==null){var e=Pu(Po),t=Be.transition,n=K;try{if(Be.transition=null,K=16>e?16:e,Nt===null)var r=!1;else{if(e=Nt,Nt=null,Po=0,Q&6)throw Error(P(331));var o=Q;for(Q|=4,M=e.current;M!==null;){var l=M,s=l.child;if(M.flags&16){var i=l.deletions;if(i!==null){for(var a=0;a<i.length;a++){var c=i[a];for(M=c;M!==null;){var g=M;switch(g.tag){case 0:case 11:case 15:nr(8,g,l)}var h=g.child;if(h!==null)h.return=g,M=h;else for(;M!==null;){g=M;var p=g.sibling,y=g.return;if(Oc(g),g===c){M=null;break}if(p!==null){p.return=y,M=p;break}M=y}}}var k=l.alternate;if(k!==null){var w=k.child;if(w!==null){k.child=null;do{var E=w.sibling;w.sibling=null,w=E}while(w!==null)}}M=l}}if(l.subtreeFlags&2064&&s!==null)s.return=l,M=s;else e:for(;M!==null;){if(l=M,l.flags&2048)switch(l.tag){case 0:case 11:case 15:nr(9,l,l.return)}var f=l.sibling;if(f!==null){f.return=l.return,M=f;break e}M=l.return}}var d=e.current;for(M=d;M!==null;){s=M;var m=s.child;if(s.subtreeFlags&2064&&m!==null)m.return=s,M=m;else e:for(s=d;M!==null;){if(i=M,i.flags&2048)try{switch(i.tag){case 0:case 11:case 15:Ho(9,i)}}catch(N){se(i,i.return,N)}if(i===s){M=null;break e}var S=i.sibling;if(S!==null){S.return=i.return,M=S;break e}M=i.return}}if(Q=o,Ot(),nt&&typeof nt.onPostCommitFiberRoot=="function")try{nt.onPostCommitFiberRoot(Fo,e)}catch{}r=!0}return r}finally{K=n,Be.transition=t}}return!1}function Pa(e,t,n){t=Dn(n,t),t=Pc(e,t,1),e=Dt(e,t,1),t=Ne(),e!==null&&(Cr(e,1,t),Me(e,t))}function se(e,t,n){if(e.tag===3)Pa(e,e,n);else for(;t!==null;){if(t.tag===3){Pa(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(_t===null||!_t.has(r))){e=Dn(n,e),e=zc(t,e,1),t=Dt(t,e,1),e=Ne(),t!==null&&(Cr(t,1,e),Me(t,e));break}}t=t.return}}function cm(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ne(),e.pingedLanes|=e.suspendedLanes&n,fe===e&&(me&n)===n&&(ce===4||ce===3&&(me&130023424)===me&&500>ie()-di?Ht(e,0):ci|=n),Me(e,t)}function Gc(e,t){t===0&&(e.mode&1?(t=Ir,Ir<<=1,!(Ir&130023424)&&(Ir=4194304)):t=1);var n=Ne();e=pt(e,t),e!==null&&(Cr(e,t,n),Me(e,n))}function dm(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Gc(e,n)}function fm(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(P(314))}r!==null&&r.delete(t),Gc(e,n)}var Xc;Xc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||De.current)ze=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ze=!1,qp(e,t,n);ze=!!(e.flags&131072)}else ze=!1,te&&t.flags&1048576&&qu(t,yo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;to(e,t),e=t.pendingProps;var o=jn(t,Se.current);Sn(t,n),o=oi(null,t,r,e,o,n);var l=li();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,_e(r)?(l=!0,go(t)):l=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,qs(t),o.updater=Uo,t.stateNode=o,o._reactInternals=t,is(t,r,e,n),t=cs(null,t,r,!0,l,n)):(t.tag=0,te&&l&&Qs(t),Ce(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(to(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=mm(r),e=Qe(r,e),o){case 0:t=us(null,t,r,e,n);break e;case 1:t=va(null,t,r,e,n);break e;case 11:t=ha(null,t,r,e,n);break e;case 14:t=ga(null,t,r,Qe(r.type,e),n);break e}throw Error(P(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Qe(r,o),us(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Qe(r,o),va(e,t,r,o,n);case 3:e:{if(Lc(t),e===null)throw Error(P(387));r=t.pendingProps,l=t.memoizedState,o=l.element,lc(e,t),So(t,r,null,n);var s=t.memoizedState;if(r=s.element,l.isDehydrated)if(l={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=l,t.memoizedState=l,t.flags&256){o=Dn(Error(P(423)),t),t=ya(e,t,r,n,o);break e}else if(r!==o){o=Dn(Error(P(424)),t),t=ya(e,t,r,n,o);break e}else for(Te=zt(t.stateNode.containerInfo.firstChild),Ie=t,te=!0,Ge=null,n=rc(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(En(),r===o){t=mt(e,t,n);break e}Ce(e,t,r,n)}t=t.child}return t;case 5:return sc(t),e===null&&os(t),r=t.type,o=t.pendingProps,l=e!==null?e.memoizedProps:null,s=o.children,ql(r,o)?s=null:l!==null&&ql(r,l)&&(t.flags|=32),Mc(e,t),Ce(e,t,s,n),t.child;case 6:return e===null&&os(t),null;case 13:return Tc(e,t,n);case 4:return ei(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Pn(t,null,r,n):Ce(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Qe(r,o),ha(e,t,r,o,n);case 7:return Ce(e,t,t.pendingProps,n),t.child;case 8:return Ce(e,t,t.pendingProps.children,n),t.child;case 12:return Ce(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,l=t.memoizedProps,s=o.value,Y(xo,r._currentValue),r._currentValue=s,l!==null)if(Ye(l.value,s)){if(l.children===o.children&&!De.current){t=mt(e,t,n);break e}}else for(l=t.child,l!==null&&(l.return=t);l!==null;){var i=l.dependencies;if(i!==null){s=l.child;for(var a=i.firstContext;a!==null;){if(a.context===r){if(l.tag===1){a=ct(-1,n&-n),a.tag=2;var c=l.updateQueue;if(c!==null){c=c.shared;var g=c.pending;g===null?a.next=a:(a.next=g.next,g.next=a),c.pending=a}}l.lanes|=n,a=l.alternate,a!==null&&(a.lanes|=n),ls(l.return,n,t),i.lanes|=n;break}a=a.next}}else if(l.tag===10)s=l.type===t.type?null:l.child;else if(l.tag===18){if(s=l.return,s===null)throw Error(P(341));s.lanes|=n,i=s.alternate,i!==null&&(i.lanes|=n),ls(s,n,t),s=l.sibling}else s=l.child;if(s!==null)s.return=l;else for(s=l;s!==null;){if(s===t){s=null;break}if(l=s.sibling,l!==null){l.return=s.return,s=l;break}s=s.return}l=s}Ce(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Sn(t,n),o=Ue(o),r=r(o),t.flags|=1,Ce(e,t,r,n),t.child;case 14:return r=t.type,o=Qe(r,t.pendingProps),o=Qe(r.type,o),ga(e,t,r,o,n);case 15:return Dc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Qe(r,o),to(e,t),t.tag=1,_e(r)?(e=!0,go(t)):e=!1,Sn(t,n),Ec(t,r,o),is(t,r,o,n),cs(null,t,r,!0,e,n);case 19:return Ic(e,t,n);case 22:return _c(e,t,n)}throw Error(P(156,t.tag))};function Zc(e,t){return Cu(e,t)}function pm(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function $e(e,t,n,r){return new pm(e,t,n,r)}function hi(e){return e=e.prototype,!(!e||!e.isReactComponent)}function mm(e){if(typeof e=="function")return hi(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Is)return 11;if(e===Rs)return 14}return 2}function Lt(e,t){var n=e.alternate;return n===null?(n=$e(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function oo(e,t,n,r,o,l){var s=2;if(r=e,typeof e=="function")hi(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case on:return Wt(n.children,o,l,t);case Ts:s=8,o|=8;break;case Ml:return e=$e(12,n,t,o|2),e.elementType=Ml,e.lanes=l,e;case Ll:return e=$e(13,n,t,o),e.elementType=Ll,e.lanes=l,e;case Tl:return e=$e(19,n,t,o),e.elementType=Tl,e.lanes=l,e;case su:return Qo(n,o,l,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ou:s=10;break e;case lu:s=9;break e;case Is:s=11;break e;case Rs:s=14;break e;case xt:s=16,r=null;break e}throw Error(P(130,e==null?e:typeof e,""))}return t=$e(s,n,t,o),t.elementType=e,t.type=r,t.lanes=l,t}function Wt(e,t,n,r){return e=$e(7,e,r,t),e.lanes=n,e}function Qo(e,t,n,r){return e=$e(22,e,r,t),e.elementType=su,e.lanes=n,e.stateNode={isHidden:!1},e}function jl(e,t,n){return e=$e(6,e,null,t),e.lanes=n,e}function El(e,t,n){return t=$e(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function hm(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=sl(0),this.expirationTimes=sl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=sl(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function gi(e,t,n,r,o,l,s,i,a){return e=new hm(e,t,n,i,a),t===1?(t=1,l===!0&&(t|=8)):t=0,l=$e(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},qs(l),e}function gm(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:rn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Yc(e){if(!e)return It;e=e._reactInternals;e:{if(qt(e)!==e||e.tag!==1)throw Error(P(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(_e(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(P(171))}if(e.tag===1){var n=e.type;if(_e(n))return Yu(e,n,t)}return t}function Jc(e,t,n,r,o,l,s,i,a){return e=gi(n,r,!0,e,o,l,s,i,a),e.context=Yc(null),n=e.current,r=Ne(),o=Mt(n),l=ct(r,o),l.callback=t??null,Dt(n,l,o),e.current.lanes=o,Cr(e,o,r),Me(e,r),e}function Ko(e,t,n,r){var o=t.current,l=Ne(),s=Mt(o);return n=Yc(n),t.context===null?t.context=n:t.pendingContext=n,t=ct(l,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Dt(o,t,s),e!==null&&(Ze(e,o,s,l),Jr(e,o,s)),s}function Do(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function za(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function vi(e,t){za(e,t),(e=e.alternate)&&za(e,t)}function vm(){return null}var qc=typeof reportError=="function"?reportError:function(e){console.error(e)};function yi(e){this._internalRoot=e}Go.prototype.render=yi.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(P(409));Ko(e,t,null,null)};Go.prototype.unmount=yi.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Zt(function(){Ko(null,e,null,null)}),t[ft]=null}};function Go(e){this._internalRoot=e}Go.prototype.unstable_scheduleHydration=function(e){if(e){var t=_u();e={blockedOn:null,target:e,priority:t};for(var n=0;n<St.length&&t!==0&&t<St[n].priority;n++);St.splice(n,0,e),n===0&&Lu(e)}};function xi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Xo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Da(){}function ym(e,t,n,r,o){if(o){if(typeof r=="function"){var l=r;r=function(){var c=Do(s);l.call(c)}}var s=Jc(t,r,e,0,null,!1,!1,"",Da);return e._reactRootContainer=s,e[ft]=s.current,pr(e.nodeType===8?e.parentNode:e),Zt(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var i=r;r=function(){var c=Do(a);i.call(c)}}var a=gi(e,0,!1,null,null,!1,!1,"",Da);return e._reactRootContainer=a,e[ft]=a.current,pr(e.nodeType===8?e.parentNode:e),Zt(function(){Ko(t,a,n,r)}),a}function Zo(e,t,n,r,o){var l=n._reactRootContainer;if(l){var s=l;if(typeof o=="function"){var i=o;o=function(){var a=Do(s);i.call(a)}}Ko(t,s,e,o)}else s=ym(n,t,e,o,r);return Do(s)}zu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Gn(t.pendingLanes);n!==0&&(Os(t,n|1),Me(t,ie()),!(Q&6)&&(_n=ie()+500,Ot()))}break;case 13:Zt(function(){var r=pt(e,1);if(r!==null){var o=Ne();Ze(r,e,1,o)}}),vi(e,1)}};Vs=function(e){if(e.tag===13){var t=pt(e,134217728);if(t!==null){var n=Ne();Ze(t,e,134217728,n)}vi(e,134217728)}};Du=function(e){if(e.tag===13){var t=Mt(e),n=pt(e,t);if(n!==null){var r=Ne();Ze(n,e,t,r)}vi(e,t)}};_u=function(){return K};Mu=function(e,t){var n=K;try{return K=e,t()}finally{K=n}};Ul=function(e,t,n){switch(t){case"input":if(Fl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=bo(r);if(!o)throw Error(P(90));au(r),Fl(r,o)}}}break;case"textarea":cu(e,n);break;case"select":t=n.value,t!=null&&vn(e,!!n.multiple,t,!1)}};vu=fi;yu=Zt;var xm={usingClientEntryPoint:!1,Events:[jr,un,bo,hu,gu,fi]},Un={findFiberByHostInstance:$t,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},wm={bundleType:Un.bundleType,version:Un.version,rendererPackageName:Un.rendererPackageName,rendererConfig:Un.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:ht.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Su(e),e===null?null:e.stateNode},findFiberByHostInstance:Un.findFiberByHostInstance||vm,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Wr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Wr.isDisabled&&Wr.supportsFiber)try{Fo=Wr.inject(wm),nt=Wr}catch{}}Fe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=xm;Fe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!xi(t))throw Error(P(200));return gm(e,t,null,n)};Fe.createRoot=function(e,t){if(!xi(e))throw Error(P(299));var n=!1,r="",o=qc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=gi(e,1,!1,null,null,n,!1,r,o),e[ft]=t.current,pr(e.nodeType===8?e.parentNode:e),new yi(t)};Fe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(P(188)):(e=Object.keys(e).join(","),Error(P(268,e)));return e=Su(t),e=e===null?null:e.stateNode,e};Fe.flushSync=function(e){return Zt(e)};Fe.hydrate=function(e,t,n){if(!Xo(t))throw Error(P(200));return Zo(null,e,t,!0,n)};Fe.hydrateRoot=function(e,t,n){if(!xi(e))throw Error(P(405));var r=n!=null&&n.hydratedSources||null,o=!1,l="",s=qc;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Jc(t,null,e,1,n??null,o,!1,l,s),e[ft]=t.current,pr(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Go(t)};Fe.render=function(e,t,n){if(!Xo(t))throw Error(P(200));return Zo(null,e,t,!1,n)};Fe.unmountComponentAtNode=function(e){if(!Xo(e))throw Error(P(40));return e._reactRootContainer?(Zt(function(){Zo(null,null,e,!1,function(){e._reactRootContainer=null,e[ft]=null})}),!0):!1};Fe.unstable_batchedUpdates=fi;Fe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xo(n))throw Error(P(200));if(e==null||e._reactInternals===void 0)throw Error(P(38));return Zo(e,t,n,!1,r)};Fe.version="18.3.1-next-f1338f8080-20240426";function ed(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(ed)}catch(e){console.error(e)}}ed(),eu.exports=Fe;var Sm=eu.exports,_a=Sm;Dl.createRoot=_a.createRoot,Dl.hydrateRoot=_a.hydrateRoot;/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var km={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cm=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),le=(e,t)=>{const n=x.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:s,className:i="",children:a,...c},g)=>x.createElement("svg",{ref:g,...km,width:o,height:o,stroke:r,strokeWidth:s?Number(l)*24/Number(o):l,className:["lucide",`lucide-${Cm(e)}`,i].join(" "),...c},[...t.map(([h,p])=>x.createElement(h,p)),...Array.isArray(a)?a:[a]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nm=le("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qr=le("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jm=le("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Em=le("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const td=le("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pm=le("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zm=le("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nd=le("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dm=le("Maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _m=le("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mm=le("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lm=le("RotateCw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rd=le("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const od=le("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tm=le("SkipBack",[["polygon",{points:"19 20 9 12 19 4 19 20",key:"o2sva"}],["line",{x1:"5",x2:"5",y1:"19",y2:"5",key:"1ocqjk"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Im=le("SkipForward",[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pl=le("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rm=le("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fm=le("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Am=le("VolumeX",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Om=le("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vm=le("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bm=le("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),$m=["mp4","avi","mov","mkv","webm","flv","m4v"],Bm=["pdf"],Um=["jpg","jpeg","png","gif","bmp","webp","svg"],Hm=["txt","md","markdown","json","xml","html","css","js","ts","py","java","cpp","c","h"];function Ma(e){if(!e)return"other";const t=e.toLowerCase();return $m.includes(t)?"video":Bm.includes(t)?"pdf":Um.includes(t)?"image":Hm.includes(t)?"text":"other"}var ld={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},La=xe.createContext&&xe.createContext(ld),Wm=["attr","size","title"];function Qm(e,t){if(e==null)return{};var n=Km(e,t),r,o;if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(o=0;o<l.length;o++)r=l[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function Km(e,t){if(e==null)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}function _o(){return _o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_o.apply(this,arguments)}function Ta(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function Mo(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Ta(Object(n),!0).forEach(function(r){Gm(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ta(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Gm(e,t,n){return t=Xm(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Xm(e){var t=Zm(e,"string");return typeof t=="symbol"?t:t+""}function Zm(e,t){if(typeof e!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t);if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function sd(e){return e&&e.map((t,n)=>xe.createElement(t.tag,Mo({key:n},t.attr),sd(t.child)))}function ge(e){return t=>xe.createElement(Ym,_o({attr:Mo({},e.attr)},t),sd(e.child))}function Ym(e){var t=n=>{var{attr:r,size:o,title:l}=e,s=Qm(e,Wm),i=o||n.size||"1em",a;return n.className&&(a=n.className),e.className&&(a=(a?a+" ":"")+e.className),xe.createElement("svg",_o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,s,{className:a,style:Mo(Mo({color:e.color||n.color},n.style),e.style),height:i,width:i,xmlns:"http://www.w3.org/2000/svg"}),l&&xe.createElement("title",null,l),e.children)};return La!==void 0?xe.createElement(La.Consumer,null,n=>t(n)):t(ld)}function Jm(e){return ge({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M0 32l34.9 395.8L192 480l157.1-52.2L384 32H0zm313.1 80l-4.8 47.3L193 208.6l-.3.1h111.5l-12.8 146.6-98.2 28.7-98.8-29.2-6.4-73.9h48.9l3.2 38.3 52.6 13.3 54.7-15.4 3.7-61.6-166.3-.5v-.1l-.2.1-3.6-46.3L193.1 162l6.5-2.7H76.7L70.9 112h242.2z"},child:[]}]})(e)}function qm(e){return ge({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M0 32l34.9 395.8L191.5 480l157.6-52.2L384 32H0zm308.2 127.9H124.4l4.1 49.4h175.6l-13.6 148.4-97.9 27v.3h-1.1l-98.7-27.3-6-75.8h47.7L138 320l53.5 14.5 53.7-14.5 6-62.2H84.3L71.5 112.2h241.1l-4.4 47.7z"},child:[]}]})(e)}function e1(e){return ge({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M0 32v448h448V32H0zm243.8 349.4c0 43.6-25.6 63.5-62.9 63.5-33.7 0-53.2-17.4-63.2-38.5l34.3-20.7c6.6 11.7 12.6 21.6 27.1 21.6 13.8 0 22.6-5.4 22.6-26.5V237.7h42.1v143.7zm99.6 63.5c-39.1 0-64.4-18.6-76.7-43l34.3-19.8c9 14.7 20.8 25.6 41.5 25.6 17.4 0 28.6-8.7 28.6-20.8 0-14.4-11.4-19.5-30.7-28l-10.5-4.5c-30.4-12.9-50.5-29.2-50.5-63.5 0-31.6 24.1-55.6 61.6-55.6 26.8 0 46 9.3 59.8 33.7L368 290c-7.2-12.9-15-18-27.1-18-12.3 0-20.1 7.8-20.1 18 0 12.6 7.8 17.7 25.9 25.6l10.5 4.5c35.8 15.3 55.9 31 55.9 66.2 0 37.8-29.8 58.6-69.7 58.6z"},child:[]}]})(e)}function t1(e){return ge({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M439.8 200.5c-7.7-30.9-22.3-54.2-53.4-54.2h-40.1v47.4c0 36.8-31.2 67.8-66.8 67.8H172.7c-29.2 0-53.4 25-53.4 54.3v101.8c0 29 25.2 46 53.4 54.3 33.8 9.9 66.3 11.7 106.8 0 26.9-7.8 53.4-23.5 53.4-54.3v-40.7H226.2v-13.6h160.2c31.1 0 42.6-21.7 53.4-54.2 11.2-33.5 10.7-65.7 0-108.6zM286.2 404c11.1 0 20.1 9.1 20.1 20.3 0 11.3-9 20.4-20.1 20.4-11 0-20.1-9.2-20.1-20.4.1-11.3 9.1-20.3 20.1-20.3zM167.8 248.1h106.8c29.7 0 53.4-24.5 53.4-54.3V91.9c0-29-24.4-50.7-53.4-55.6-35.8-5.9-74.7-5.6-106.8.1-45.2 8-53.4 24.7-53.4 55.6v40.7h106.9v13.6h-147c-31.1 0-58.3 18.7-66.8 54.2-9.8 40.7-10.2 66.1 0 108.6 7.6 31.6 25.7 54.2 56.8 54.2H101v-48.8c0-35.3 30.5-66.4 66.8-66.4zm-6.7-142.6c-11.1 0-20.1-9.1-20.1-20.3.1-11.3 9-20.4 20.1-20.4 11 0 20.1 9.2 20.1 20.4s-9 20.3-20.1 20.3z"},child:[]}]})(e)}function n1(e){return ge({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M418.2 177.2c-5.4-1.8-10.8-3.5-16.2-5.1.9-3.7 1.7-7.4 2.5-11.1 12.3-59.6 4.2-107.5-23.1-123.3-26.3-15.1-69.2.6-112.6 38.4-4.3 3.7-8.5 7.6-12.5 11.5-2.7-2.6-5.5-5.2-8.3-7.7-45.5-40.4-91.1-57.4-118.4-41.5-26.2 15.2-34 60.3-23 116.7 1.1 5.6 2.3 11.1 3.7 16.7-6.4 1.8-12.7 3.8-18.6 5.9C38.3 196.2 0 225.4 0 255.6c0 31.2 40.8 62.5 96.3 81.5 4.5 1.5 9 3 13.6 4.3-1.5 6-2.8 11.9-4 18-10.5 55.5-2.3 99.5 23.9 114.6 27 15.6 72.4-.4 116.6-39.1 3.5-3.1 7-6.3 10.5-9.7 4.4 4.3 9 8.4 13.6 12.4 42.8 36.8 85.1 51.7 111.2 36.6 27-15.6 35.8-62.9 24.4-120.5-.9-4.4-1.9-8.9-3-13.5 3.2-.9 6.3-1.9 9.4-2.9 57.7-19.1 99.5-50 99.5-81.7 0-30.3-39.4-59.7-93.8-78.4zM282.9 92.3c37.2-32.4 71.9-45.1 87.7-36 16.9 9.7 23.4 48.9 12.8 100.4-.7 3.4-1.4 6.7-2.3 10-22.2-5-44.7-8.6-67.3-10.6-13-18.6-27.2-36.4-42.6-53.1 3.9-3.7 7.7-7.2 11.7-10.7zM167.2 307.5c5.1 8.7 10.3 17.4 15.8 25.9-15.6-1.7-31.1-4.2-46.4-7.5 4.4-14.4 9.9-29.3 16.3-44.5 4.6 8.8 9.3 17.5 14.3 26.1zm-30.3-120.3c14.4-3.2 29.7-5.8 45.6-7.8-5.3 8.3-10.5 16.8-15.4 25.4-4.9 8.5-9.7 17.2-14.2 26-6.3-14.9-11.6-29.5-16-43.6zm27.4 68.9c6.6-13.8 13.8-27.3 21.4-40.6s15.8-26.2 24.4-38.9c15-1.1 30.3-1.7 45.9-1.7s31 .6 45.9 1.7c8.5 12.6 16.6 25.5 24.3 38.7s14.9 26.7 21.7 40.4c-6.7 13.8-13.9 27.4-21.6 40.8-7.6 13.3-15.7 26.2-24.2 39-14.9 1.1-30.4 1.6-46.1 1.6s-30.9-.5-45.6-1.4c-8.7-12.7-16.9-25.7-24.6-39s-14.8-26.8-21.5-40.6zm180.6 51.2c5.1-8.8 9.9-17.7 14.6-26.7 6.4 14.5 12 29.2 16.9 44.3-15.5 3.5-31.2 6.2-47 8 5.4-8.4 10.5-17 15.5-25.6zm14.4-76.5c-4.7-8.8-9.5-17.6-14.5-26.2-4.9-8.5-10-16.9-15.3-25.2 16.1 2 31.5 4.7 45.9 8-4.6 14.8-10 29.2-16.1 43.4zM256.2 118.3c10.5 11.4 20.4 23.4 29.6 35.8-19.8-.9-39.7-.9-59.5 0 9.8-12.9 19.9-24.9 29.9-35.8zM140.2 57c16.8-9.8 54.1 4.2 93.4 39 2.5 2.2 5 4.6 7.6 7-15.5 16.7-29.8 34.5-42.9 53.1-22.6 2-45 5.5-67.2 10.4-1.3-5.1-2.4-10.3-3.5-15.5-9.4-48.4-3.2-84.9 12.6-94zm-24.5 263.6c-4.2-1.2-8.3-2.5-12.4-3.9-21.3-6.7-45.5-17.3-63-31.2-10.1-7-16.9-17.8-18.8-29.9 0-18.3 31.6-41.7 77.2-57.6 5.7-2 11.5-3.8 17.3-5.5 6.8 21.7 15 43 24.5 63.6-9.6 20.9-17.9 42.5-24.8 64.5zm116.6 98c-16.5 15.1-35.6 27.1-56.4 35.3-11.1 5.3-23.9 5.8-35.3 1.3-15.9-9.2-22.5-44.5-13.5-92 1.1-5.6 2.3-11.2 3.7-16.7 22.4 4.8 45 8.1 67.9 9.8 13.2 18.7 27.7 36.6 43.2 53.4-3.2 3.1-6.4 6.1-9.6 8.9zm24.5-24.3c-10.2-11-20.4-23.2-30.3-36.3 9.6.4 19.5.6 29.5.6 10.3 0 20.4-.2 30.4-.7-9.2 12.7-19.1 24.8-29.6 36.4zm130.7 30c-.9 12.2-6.9 23.6-16.5 31.3-15.9 9.2-49.8-2.8-86.4-34.2-4.2-3.6-8.4-7.5-12.7-11.5 15.3-16.9 29.4-34.8 42.2-53.6 22.9-1.9 45.7-5.4 68.2-10.5 1 4.1 1.9 8.2 2.7 12.2 4.9 21.6 5.7 44.1 2.5 66.3zm18.2-107.5c-2.8.9-5.6 1.8-8.5 2.6-7-21.8-15.6-43.1-25.5-63.8 9.6-20.4 17.7-41.4 24.5-62.9 5.2 1.5 10.2 3.1 15 4.7 46.6 16 79.3 39.8 79.3 58 0 19.6-34.9 44.9-84.8 61.4zm-149.7-15c25.3 0 45.8-20.5 45.8-45.8s-20.5-45.8-45.8-45.8c-25.3 0-45.8 20.5-45.8 45.8s20.5 45.8 45.8 45.8z"},child:[]}]})(e)}function r1(e){return ge({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm64 236c0 6.6-5.4 12-12 12H108c-6.6 0-12-5.4-12-12v-8c0-6.6 5.4-12 12-12h168c6.6 0 12 5.4 12 12v8zm0-64c0 6.6-5.4 12-12 12H108c-6.6 0-12-5.4-12-12v-8c0-6.6 5.4-12 12-12h168c6.6 0 12 5.4 12 12v8zm0-72v8c0 6.6-5.4 12-12 12H108c-6.6 0-12-5.4-12-12v-8c0-6.6 5.4-12 12-12h168c6.6 0 12 5.4 12 12zm96-114.1v6.1H256V0h6.1c6.4 0 12.5 2.5 17 7l97.9 98c4.5 4.5 7 10.6 7 16.9z"},child:[]}]})(e)}function o1(e){return ge({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm-64 268c0 10.7-12.9 16-20.5 8.5L104 376H76c-6.6 0-12-5.4-12-12v-56c0-6.6 5.4-12 12-12h28l35.5-36.5c7.6-7.6 20.5-2.2 20.5 8.5v136zm33.2-47.6c9.1-9.3 9.1-24.1 0-33.4-22.1-22.8 12.2-56.2 34.4-33.5 27.2 27.9 27.2 72.4 0 100.4-21.8 22.3-56.9-10.4-34.4-33.5zm86-117.1c54.4 55.9 54.4 144.8 0 200.8-21.8 22.4-57-10.3-34.4-33.5 36.2-37.2 36.3-96.5 0-133.8-22.1-22.8 12.3-56.3 34.4-33.5zM384 121.9v6.1H256V0h6.1c6.4 0 12.5 2.5 17 7l97.9 98c4.5 4.5 7 10.6 7 16.9z"},child:[]}]})(e)}function l1(e){return ge({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M384 121.941V128H256V0h6.059c6.365 0 12.47 2.529 16.971 7.029l97.941 97.941A24.005 24.005 0 0 1 384 121.941zM248 160c-13.2 0-24-10.8-24-24V0H24C10.745 0 0 10.745 0 24v464c0 13.255 10.745 24 24 24h336c13.255 0 24-10.745 24-24V160H248zM123.206 400.505a5.4 5.4 0 0 1-7.633.246l-64.866-60.812a5.4 5.4 0 0 1 0-7.879l64.866-60.812a5.4 5.4 0 0 1 7.633.246l19.579 20.885a5.4 5.4 0 0 1-.372 7.747L101.65 336l40.763 35.874a5.4 5.4 0 0 1 .372 7.747l-19.579 20.884zm51.295 50.479l-27.453-7.97a5.402 5.402 0 0 1-3.681-6.692l61.44-211.626a5.402 5.402 0 0 1 6.692-3.681l27.452 7.97a5.4 5.4 0 0 1 3.68 6.692l-61.44 211.626a5.397 5.397 0 0 1-6.69 3.681zm160.792-111.045l-64.866 60.812a5.4 5.4 0 0 1-7.633-.246l-19.58-20.885a5.4 5.4 0 0 1 .372-7.747L284.35 336l-40.763-35.874a5.4 5.4 0 0 1-.372-7.747l19.58-20.885a5.4 5.4 0 0 1 7.633-.246l64.866 60.812a5.4 5.4 0 0 1-.001 7.879z"},child:[]}]})(e)}function s1(e){return ge({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M384 121.941V128H256V0h6.059a24 24 0 0 1 16.97 7.029l97.941 97.941a24.002 24.002 0 0 1 7.03 16.971zM248 160c-13.2 0-24-10.8-24-24V0H24C10.745 0 0 10.745 0 24v464c0 13.255 10.745 24 24 24h336c13.255 0 24-10.745 24-24V160H248zm-135.455 16c26.51 0 48 21.49 48 48s-21.49 48-48 48-48-21.49-48-48 21.491-48 48-48zm208 240h-256l.485-48.485L104.545 328c4.686-4.686 11.799-4.201 16.485.485L160.545 368 264.06 264.485c4.686-4.686 12.284-4.686 16.971 0L320.545 304v112z"},child:[]}]})(e)}function i1(e){return ge({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M181.9 256.1c-5-16-4.9-46.9-2-46.9 8.4 0 7.6 36.9 2 46.9zm-1.7 47.2c-7.7 20.2-17.3 43.3-28.4 62.7 18.3-7 39-17.2 62.9-21.9-12.7-9.6-24.9-23.4-34.5-40.8zM86.1 428.1c0 .8 13.2-5.4 34.9-40.2-6.7 6.3-29.1 24.5-34.9 40.2zM248 160h136v328c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24V24C0 10.7 10.7 0 24 0h200v136c0 13.2 10.8 24 24 24zm-8 171.8c-20-12.2-33.3-29-42.7-53.8 4.5-18.5 11.6-46.6 6.2-64.2-4.7-29.4-42.4-26.5-47.8-6.8-5 18.3-.4 44.1 8.1 77-11.6 27.6-28.7 64.6-40.8 85.8-.1 0-.1.1-.2.1-27.1 13.9-73.6 44.5-54.5 68 5.6 6.9 16 10 21.5 10 17.9 0 35.7-18 61.1-61.8 25.8-8.5 54.1-19.1 79-23.2 21.7 11.8 47.1 19.5 64 19.5 29.2 0 31.2-32 19.7-43.4-13.9-13.6-54.3-9.7-73.6-7.2zM377 105L279 7c-4.5-4.5-10.6-7-17-7h-6v128h128v-6.1c0-6.3-2.5-12.4-7-16.9zm-74.1 255.3c4.1-2.7-2.5-11.9-42.8-9 37.1 15.8 42.8 9 42.8 9z"},child:[]}]})(e)}function a1(e){return ge({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M384 121.941V128H256V0h6.059c6.365 0 12.47 2.529 16.971 7.029l97.941 97.941A24.005 24.005 0 0 1 384 121.941zM224 136V0H24C10.745 0 0 10.745 0 24v464c0 13.255 10.745 24 24 24h336c13.255 0 24-10.745 24-24V160H248c-13.2 0-24-10.8-24-24zm96 144.016v111.963c0 21.445-25.943 31.998-40.971 16.971L224 353.941V392c0 13.255-10.745 24-24 24H88c-13.255 0-24-10.745-24-24V280c0-13.255 10.745-24 24-24h112c13.255 0 24 10.745 24 24v38.059l55.029-55.013c15.011-15.01 40.971-4.491 40.971 16.97z"},child:[]}]})(e)}function Ia(e){return ge({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm160-14.1v6.1H256V0h6.1c6.4 0 12.5 2.5 17 7l97.9 98c4.5 4.5 7 10.6 7 16.9z"},child:[]}]})(e)}function u1(e){return ge({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M572.694 292.093L500.27 416.248A63.997 63.997 0 0 1 444.989 448H45.025c-18.523 0-30.064-20.093-20.731-36.093l72.424-124.155A64 64 0 0 1 152 256h399.964c18.523 0 30.064 20.093 20.73 36.093zM152 224h328v-48c0-26.51-21.49-48-48-48H272l-64-64H48C21.49 64 0 85.49 0 112v278.046l69.077-118.418C86.214 242.25 117.989 224 152 224z"},child:[]}]})(e)}function id(e){return ge({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M464 128H272l-64-64H48C21.49 64 0 85.49 0 112v288c0 26.51 21.49 48 48 48h416c26.51 0 48-21.49 48-48V176c0-26.51-21.49-48-48-48z"},child:[]}]})(e)}function ad(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=ad(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function ud(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=ad(e))&&(r&&(r+=" "),r+=t);return r}const wi="-",c1=e=>{const t=f1(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:s=>{const i=s.split(wi);return i[0]===""&&i.length!==1&&i.shift(),cd(i,t)||d1(s)},getConflictingClassGroupIds:(s,i)=>{const a=n[s]||[];return i&&r[s]?[...a,...r[s]]:a}}},cd=(e,t)=>{var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?cd(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const l=e.join(wi);return(s=t.validators.find(({validator:i})=>i(l)))==null?void 0:s.classGroupId},Ra=/^\[(.+)\]$/,d1=e=>{if(Ra.test(e)){const t=Ra.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},f1=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return m1(Object.entries(e.classGroups),n).forEach(([l,s])=>{ks(s,r,l,t)}),r},ks=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const l=o===""?t:Fa(t,o);l.classGroupId=n;return}if(typeof o=="function"){if(p1(o)){ks(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([l,s])=>{ks(s,Fa(t,l),n,r)})})},Fa=(e,t)=>{let n=e;return t.split(wi).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},p1=e=>e.isThemeGetter,m1=(e,t)=>t?e.map(([n,r])=>{const o=r.map(l=>typeof l=="string"?t+l:typeof l=="object"?Object.fromEntries(Object.entries(l).map(([s,i])=>[t+s,i])):l);return[n,o]}):e,h1=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(l,s)=>{n.set(l,s),t++,t>e&&(t=0,r=n,n=new Map)};return{get(l){let s=n.get(l);if(s!==void 0)return s;if((s=r.get(l))!==void 0)return o(l,s),s},set(l,s){n.has(l)?n.set(l,s):o(l,s)}}},dd="!",g1=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],l=t.length,s=i=>{const a=[];let c=0,g=0,h;for(let E=0;E<i.length;E++){let f=i[E];if(c===0){if(f===o&&(r||i.slice(E,E+l)===t)){a.push(i.slice(g,E)),g=E+l;continue}if(f==="/"){h=E;continue}}f==="["?c++:f==="]"&&c--}const p=a.length===0?i:i.substring(g),y=p.startsWith(dd),k=y?p.substring(1):p,w=h&&h>g?h-g:void 0;return{modifiers:a,hasImportantModifier:y,baseClassName:k,maybePostfixModifierPosition:w}};return n?i=>n({className:i,parseClassName:s}):s},v1=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},y1=e=>({cache:h1(e.cacheSize),parseClassName:g1(e),...c1(e)}),x1=/\s+/,w1=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,l=[],s=e.trim().split(x1);let i="";for(let a=s.length-1;a>=0;a-=1){const c=s[a],{modifiers:g,hasImportantModifier:h,baseClassName:p,maybePostfixModifierPosition:y}=n(c);let k=!!y,w=r(k?p.substring(0,y):p);if(!w){if(!k){i=c+(i.length>0?" "+i:i);continue}if(w=r(p),!w){i=c+(i.length>0?" "+i:i);continue}k=!1}const E=v1(g).join(":"),f=h?E+dd:E,d=f+w;if(l.includes(d))continue;l.push(d);const m=o(w,k);for(let S=0;S<m.length;++S){const N=m[S];l.push(f+N)}i=c+(i.length>0?" "+i:i)}return i};function S1(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=fd(t))&&(r&&(r+=" "),r+=n);return r}const fd=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=fd(e[r]))&&(n&&(n+=" "),n+=t);return n};function k1(e,...t){let n,r,o,l=s;function s(a){const c=t.reduce((g,h)=>h(g),e());return n=y1(c),r=n.cache.get,o=n.cache.set,l=i,i(a)}function i(a){const c=r(a);if(c)return c;const g=w1(a,n);return o(a,g),g}return function(){return l(S1.apply(null,arguments))}}const J=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},pd=/^\[(?:([a-z-]+):)?(.+)\]$/i,C1=/^\d+\/\d+$/,N1=new Set(["px","full","screen"]),j1=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,E1=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,P1=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,z1=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,D1=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,lt=e=>Cn(e)||N1.has(e)||C1.test(e),vt=e=>In(e,"length",A1),Cn=e=>!!e&&!Number.isNaN(Number(e)),zl=e=>In(e,"number",Cn),Hn=e=>!!e&&Number.isInteger(Number(e)),_1=e=>e.endsWith("%")&&Cn(e.slice(0,-1)),b=e=>pd.test(e),yt=e=>j1.test(e),M1=new Set(["length","size","percentage"]),L1=e=>In(e,M1,md),T1=e=>In(e,"position",md),I1=new Set(["image","url"]),R1=e=>In(e,I1,V1),F1=e=>In(e,"",O1),Wn=()=>!0,In=(e,t,n)=>{const r=pd.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},A1=e=>E1.test(e)&&!P1.test(e),md=()=>!1,O1=e=>z1.test(e),V1=e=>D1.test(e),b1=()=>{const e=J("colors"),t=J("spacing"),n=J("blur"),r=J("brightness"),o=J("borderColor"),l=J("borderRadius"),s=J("borderSpacing"),i=J("borderWidth"),a=J("contrast"),c=J("grayscale"),g=J("hueRotate"),h=J("invert"),p=J("gap"),y=J("gradientColorStops"),k=J("gradientColorStopPositions"),w=J("inset"),E=J("margin"),f=J("opacity"),d=J("padding"),m=J("saturate"),S=J("scale"),N=J("sepia"),z=J("skew"),C=J("space"),D=J("translate"),A=()=>["auto","contain","none"],v=()=>["auto","hidden","clip","visible","scroll"],_=()=>["auto",b,t],R=()=>[b,t],H=()=>["",lt,vt],X=()=>["auto",Cn,b],I=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],O=()=>["solid","dashed","dotted","double","none"],V=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],j=()=>["start","end","center","between","around","evenly","stretch"],T=()=>["","0",b],F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],$=()=>[Cn,b];return{cacheSize:500,separator:":",theme:{colors:[Wn],spacing:[lt,vt],blur:["none","",yt,b],brightness:$(),borderColor:[e],borderRadius:["none","","full",yt,b],borderSpacing:R(),borderWidth:H(),contrast:$(),grayscale:T(),hueRotate:$(),invert:T(),gap:R(),gradientColorStops:[e],gradientColorStopPositions:[_1,vt],inset:_(),margin:_(),opacity:$(),padding:R(),saturate:$(),scale:$(),sepia:T(),skew:$(),space:R(),translate:R()},classGroups:{aspect:[{aspect:["auto","square","video",b]}],container:["container"],columns:[{columns:[yt]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...I(),b]}],overflow:[{overflow:v()}],"overflow-x":[{"overflow-x":v()}],"overflow-y":[{"overflow-y":v()}],overscroll:[{overscroll:A()}],"overscroll-x":[{"overscroll-x":A()}],"overscroll-y":[{"overscroll-y":A()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[w]}],"inset-x":[{"inset-x":[w]}],"inset-y":[{"inset-y":[w]}],start:[{start:[w]}],end:[{end:[w]}],top:[{top:[w]}],right:[{right:[w]}],bottom:[{bottom:[w]}],left:[{left:[w]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Hn,b]}],basis:[{basis:_()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",b]}],grow:[{grow:T()}],shrink:[{shrink:T()}],order:[{order:["first","last","none",Hn,b]}],"grid-cols":[{"grid-cols":[Wn]}],"col-start-end":[{col:["auto",{span:["full",Hn,b]},b]}],"col-start":[{"col-start":X()}],"col-end":[{"col-end":X()}],"grid-rows":[{"grid-rows":[Wn]}],"row-start-end":[{row:["auto",{span:[Hn,b]},b]}],"row-start":[{"row-start":X()}],"row-end":[{"row-end":X()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",b]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",b]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...j()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...j(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...j(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[d]}],px:[{px:[d]}],py:[{py:[d]}],ps:[{ps:[d]}],pe:[{pe:[d]}],pt:[{pt:[d]}],pr:[{pr:[d]}],pb:[{pb:[d]}],pl:[{pl:[d]}],m:[{m:[E]}],mx:[{mx:[E]}],my:[{my:[E]}],ms:[{ms:[E]}],me:[{me:[E]}],mt:[{mt:[E]}],mr:[{mr:[E]}],mb:[{mb:[E]}],ml:[{ml:[E]}],"space-x":[{"space-x":[C]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[C]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",b,t]}],"min-w":[{"min-w":[b,t,"min","max","fit"]}],"max-w":[{"max-w":[b,t,"none","full","min","max","fit","prose",{screen:[yt]},yt]}],h:[{h:[b,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[b,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[b,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[b,t,"auto","min","max","fit"]}],"font-size":[{text:["base",yt,vt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",zl]}],"font-family":[{font:[Wn]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",b]}],"line-clamp":[{"line-clamp":["none",Cn,zl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",lt,b]}],"list-image":[{"list-image":["none",b]}],"list-style-type":[{list:["none","disc","decimal",b]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[f]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[f]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...O(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",lt,vt]}],"underline-offset":[{"underline-offset":["auto",lt,b]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:R()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",b]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",b]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[f]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...I(),T1]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",L1]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},R1]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[k]}],"gradient-via-pos":[{via:[k]}],"gradient-to-pos":[{to:[k]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[f]}],"border-style":[{border:[...O(),"hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[f]}],"divide-style":[{divide:O()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...O()]}],"outline-offset":[{"outline-offset":[lt,b]}],"outline-w":[{outline:[lt,vt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:H()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[f]}],"ring-offset-w":[{"ring-offset":[lt,vt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",yt,F1]}],"shadow-color":[{shadow:[Wn]}],opacity:[{opacity:[f]}],"mix-blend":[{"mix-blend":[...V(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":V()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",yt,b]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[g]}],invert:[{invert:[h]}],saturate:[{saturate:[m]}],sepia:[{sepia:[N]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[g]}],"backdrop-invert":[{"backdrop-invert":[h]}],"backdrop-opacity":[{"backdrop-opacity":[f]}],"backdrop-saturate":[{"backdrop-saturate":[m]}],"backdrop-sepia":[{"backdrop-sepia":[N]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",b]}],duration:[{duration:$()}],ease:[{ease:["linear","in","out","in-out",b]}],delay:[{delay:$()}],animate:[{animate:["none","spin","ping","pulse","bounce",b]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[Hn,b]}],"translate-x":[{"translate-x":[D]}],"translate-y":[{"translate-y":[D]}],"skew-x":[{"skew-x":[z]}],"skew-y":[{"skew-y":[z]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",b]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",b]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":R()}],"scroll-mx":[{"scroll-mx":R()}],"scroll-my":[{"scroll-my":R()}],"scroll-ms":[{"scroll-ms":R()}],"scroll-me":[{"scroll-me":R()}],"scroll-mt":[{"scroll-mt":R()}],"scroll-mr":[{"scroll-mr":R()}],"scroll-mb":[{"scroll-mb":R()}],"scroll-ml":[{"scroll-ml":R()}],"scroll-p":[{"scroll-p":R()}],"scroll-px":[{"scroll-px":R()}],"scroll-py":[{"scroll-py":R()}],"scroll-ps":[{"scroll-ps":R()}],"scroll-pe":[{"scroll-pe":R()}],"scroll-pt":[{"scroll-pt":R()}],"scroll-pr":[{"scroll-pr":R()}],"scroll-pb":[{"scroll-pb":R()}],"scroll-pl":[{"scroll-pl":R()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",b]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[lt,vt,zl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},$1=k1(b1);function B1(e){return ge({attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M12.043 23.968c.479-.004.953-.029 1.426-.094a11.805 11.805 0 003.146-.863 12.404 12.404 0 003.793-2.542 11.977 11.977 0 002.44-3.427 11.794 11.794 0 001.02-3.476c.149-1.16.135-2.346-.045-3.499a11.96 11.96 0 00-.793-2.788 11.197 11.197 0 00-.854-1.617c-1.168-1.837-2.861-3.314-4.81-4.3a12.835 12.835 0 00-2.172-.87h-.005c.119.063.24.132.345.201.12.074.239.146.351.225a8.93 8.93 0 011.559 1.33c1.063 1.145 1.797 2.548 2.218 4.041.284.982.434 1.998.495 3.017.044.743.044 1.491-.047 2.229-.149 1.27-.554 2.51-1.228 3.596a7.475 7.475 0 01-1.903 2.084c-1.244.928-2.877 1.482-4.436 1.114a3.916 3.916 0 01-.748-.258 4.692 4.692 0 01-.779-.45 6.08 6.08 0 01-1.244-1.105 6.507 6.507 0 01-1.049-1.747 7.366 7.366 0 01-.494-2.54c-.03-1.273.225-2.553.854-3.67a6.43 6.43 0 011.663-1.918c.225-.178.464-.333.704-.479l.016-.007a5.121 5.121 0 00-1.441-.12 4.963 4.963 0 00-1.228.24c-.359.12-.704.27-1.019.45a6.146 6.146 0 00-.733.494c-.211.18-.42.36-.615.555-1.123 1.153-1.768 2.682-2.022 4.256-.15.973-.15 1.96-.091 2.95.105 1.395.391 2.787.945 4.062a8.518 8.518 0 001.348 2.173 8.14 8.14 0 003.132 2.23 7.934 7.934 0 002.113.54c.074.015.149.015.209.015zm-2.934-.398a4.102 4.102 0 01-.45-.228 8.5 8.5 0 01-2.038-1.534c-1.094-1.137-1.827-2.566-2.247-4.08a15.184 15.184 0 01-.495-3.172 12.14 12.14 0 01.046-2.082c.135-1.257.495-2.501 1.124-3.58a6.889 6.889 0 011.783-2.053 6.23 6.23 0 011.633-.9 5.363 5.363 0 013.522-.045c.029 0 .029 0 .045.03.015.015.045.015.06.03.045.016.104.045.165.074.239.12.479.271.704.42a6.294 6.294 0 012.097 2.502c.42.914.615 1.934.631 2.938.014 1.079-.18 2.157-.645 3.146a6.42 6.42 0 01-2.638 2.832c.09.03.18.045.271.075.225.044.449.074.688.074 1.468.045 2.892-.66 3.94-1.647.195-.18.375-.375.54-.585.225-.27.435-.54.614-.823.239-.375.435-.75.614-1.154a8.112 8.112 0 00.509-1.664c.196-1.004.211-2.022.149-3.026-.135-2.022-.673-4.045-1.842-5.724a9.054 9.054 0 00-.555-.719 9.868 9.868 0 00-1.063-1.034 8.477 8.477 0 00-1.363-.915 9.927 9.927 0 00-1.692-.598l-.3-.06c-.209-.03-.42-.044-.634-.06a8.453 8.453 0 00-1.015.016c-.704.045-1.412.16-2.112.337C5.799 1.227 2.863 3.566 1.3 6.67A11.834 11.834 0 00.238 9.801a11.81 11.81 0 00-.104 3.775c.12 1.02.374 2.023.778 2.977.227.57.511 1.124.825 1.648 1.094 1.783 2.683 3.236 4.51 4.24.688.39 1.408.69 2.157.944.226.074.45.15.689.21z"},child:[]}]})(e)}function U1(e){return ge({attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M22.27 19.385H1.73A1.73 1.73 0 010 17.655V6.345a1.73 1.73 0 011.73-1.73h20.54A1.73 1.73 0 0124 6.345v11.308a1.73 1.73 0 01-1.73 1.731zM5.769 15.923v-4.5l2.308 2.885 2.307-2.885v4.5h2.308V8.078h-2.308l-2.307 2.885-2.308-2.885H3.46v7.847zM21.232 12h-2.309V8.077h-2.307V12h-2.308l3.461 4.039z"},child:[]}]})(e)}function H1(e){return ge({attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M1.125 0C.502 0 0 .502 0 1.125v21.75C0 23.498.502 24 1.125 24h21.75c.623 0 1.125-.502 1.125-1.125V1.125C24 .502 23.498 0 22.875 0zm17.363 9.75c.612 0 1.154.037 1.627.111a6.38 6.38 0 0 1 1.306.34v2.458a3.95 3.95 0 0 0-.643-.361 5.093 5.093 0 0 0-.717-.26 5.453 5.453 0 0 0-1.426-.2c-.3 0-.573.028-.819.086a2.1 2.1 0 0 0-.623.242c-.17.104-.3.229-.393.374a.888.888 0 0 0-.14.49c0 .196.053.373.156.529.104.156.252.304.443.444s.423.276.696.41c.273.135.582.274.926.416.47.197.892.407 1.266.628.374.222.695.473.963.753.268.279.472.598.614.957.142.359.214.776.214 1.253 0 .657-.125 1.21-.373 1.656a3.033 3.033 0 0 1-1.012 1.085 4.38 4.38 0 0 1-1.487.596c-.566.12-1.163.18-1.79.18a9.916 9.916 0 0 1-1.84-.164 5.544 5.544 0 0 1-1.512-.493v-2.63a5.033 5.033 0 0 0 3.237 1.2c.333 0 .624-.03.872-.09.249-.06.456-.144.623-.25.166-.108.29-.234.373-.38a1.023 1.023 0 0 0-.074-1.089 2.12 2.12 0 0 0-.537-.5 5.597 5.597 0 0 0-.807-.444 27.72 27.72 0 0 0-1.007-.436c-.918-.383-1.602-.852-2.053-1.405-.45-.553-.676-1.222-.676-2.005 0-.614.123-1.141.369-1.582.246-.441.58-.804 1.004-1.089a4.494 4.494 0 0 1 1.47-.629 7.536 7.536 0 0 1 1.77-.201zm-15.113.188h9.563v2.166H9.506v9.646H6.789v-9.646H3.375z"},child:[]}]})(e)}function Yt(...e){return $1(ud(e))}function hd(e){if(e===0)return"0 Bytes";const t=1024,n=["Bytes","KB","MB","GB","TB"],r=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,r)).toFixed(2))+" "+n[r]}function Aa(e){const t=Math.floor(e/3600),n=Math.floor(e%3600/60),r=Math.floor(e%60);return t>0?`${t}:${n.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`:`${n}:${r.toString().padStart(2,"0")}`}function Lo(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}).format(e)}function W1(e,t,n=!1){if(t)return n?u1:id;if(!e)return Ia;const r=e.toLowerCase();return["mp4","avi","mov","mkv","webm","flv","m4v"].includes(r)?a1:r==="pdf"?i1:["jpg","jpeg","png","gif","bmp","webp","svg"].includes(r)?s1:["mp3","wav","flac","aac","ogg"].includes(r)?o1:r==="js"?e1:r==="ts"?H1:r==="tsx"||r==="jsx"?n1:r==="py"?t1:r==="html"?qm:r==="css"?Jm:r==="json"?B1:r==="md"||r==="markdown"?U1:["txt","md","markdown"].includes(r)?r1:["js","ts","jsx","tsx","html","css","json","xml","py","java","cpp","c","h"].includes(r)?l1:Ia}function Oa(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function gd(...e){return t=>{let n=!1;const r=e.map(o=>{const l=Oa(o,t);return!n&&typeof l=="function"&&(n=!0),l});if(n)return()=>{for(let o=0;o<r.length;o++){const l=r[o];typeof l=="function"?l():Oa(e[o],null)}}}}function Rt(...e){return x.useCallback(gd(...e),e)}function To(e){const t=K1(e),n=x.forwardRef((r,o)=>{const{children:l,...s}=r,i=x.Children.toArray(l),a=i.find(X1);if(a){const c=a.props.children,g=i.map(h=>h===a?x.Children.count(c)>1?x.Children.only(null):x.isValidElement(c)?c.props.children:null:h);return u.jsx(t,{...s,ref:o,children:x.isValidElement(c)?x.cloneElement(c,void 0,g):null})}return u.jsx(t,{...s,ref:o,children:l})});return n.displayName=`${e}.Slot`,n}var Q1=To("Slot");function K1(e){const t=x.forwardRef((n,r)=>{const{children:o,...l}=n;if(x.isValidElement(o)){const s=Y1(o),i=Z1(l,o.props);return o.type!==x.Fragment&&(i.ref=r?gd(r,s):s),x.cloneElement(o,i)}return x.Children.count(o)>1?x.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var G1=Symbol("radix.slottable");function X1(e){return x.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===G1}function Z1(e,t){const n={...t};for(const r in t){const o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...i)=>{const a=l(...i);return o(...i),a}:o&&(n[r]=o):r==="style"?n[r]={...o,...l}:r==="className"&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}function Y1(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}const Va=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,ba=ud,J1=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return ba(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:l}=t,s=Object.keys(o).map(c=>{const g=n==null?void 0:n[c],h=l==null?void 0:l[c];if(g===null)return null;const p=Va(g)||Va(h);return o[c][p]}),i=n&&Object.entries(n).reduce((c,g)=>{let[h,p]=g;return p===void 0||(c[h]=p),c},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((c,g)=>{let{class:h,className:p,...y}=g;return Object.entries(y).every(k=>{let[w,E]=k;return Array.isArray(E)?E.includes({...l,...i}[w]):{...l,...i}[w]===E})?[...c,h,p]:c},[]);return ba(e,s,a,n==null?void 0:n.class,n==null?void 0:n.className)},q1=J1("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Z=x.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},l)=>{const s=r?Q1:"button";return u.jsx(s,{className:Yt(q1({variant:t,size:n,className:e})),ref:l,...o})});Z.displayName="Button";const Yo=x.forwardRef(({className:e,type:t,...n},r)=>u.jsx("input",{type:t,className:Yt("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));Yo.displayName="Input";const e0="modulepreload",t0=function(e,t){return new URL(e,t).href},$a={},vd=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){const s=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),a=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));o=Promise.allSettled(n.map(c=>{if(c=t0(c,r),c in $a)return;$a[c]=!0;const g=c.endsWith(".css"),h=g?'[rel="stylesheet"]':"";if(!!r)for(let k=s.length-1;k>=0;k--){const w=s[k];if(w.href===c&&(!g||w.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${c}"]${h}`))return;const y=document.createElement("link");if(y.rel=g?"stylesheet":e0,g||(y.as="script"),y.crossOrigin="",y.href=c,a&&y.setAttribute("nonce",a),document.head.appendChild(y),g)return new Promise((k,w)=>{y.addEventListener("load",k),y.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${c}`)))})}))}function l(s){const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=s,window.dispatchEvent(i),!i.defaultPrevented)throw s}return o.then(s=>{for(const i of s||[])i.status==="rejected"&&l(i.reason);return t().catch(l)})},n0=typeof window<"u"&&!window.electronAPI;class r0{constructor(){el(this,"db",null);el(this,"isInitialized",!1)}async initialize(){this.isInitialized||(this.isInitialized=!0,await this.createTables(),n0&&await this.initializeMockData())}async createTables(){const t=[{key:"videoPlaybackSpeed",value:"1"},{key:"autoMarkAsLearned",value:"true"},{key:"theme",value:"light"},{key:"lastOpenedDirectory",value:""}];for(const n of t)localStorage.getItem(`setting_${n.key}`)||localStorage.setItem(`setting_${n.key}`,n.value)}async initializeMockData(){if(!localStorage.getItem("learning_progress_initialized")){const{mockProgress:t,mockSettings:n}=await vd(async()=>{const{mockProgress:r,mockSettings:o}=await Promise.resolve().then(()=>U0);return{mockProgress:r,mockSettings:o}},void 0,import.meta.url);t.forEach((r,o)=>{localStorage.setItem(`progress_${o}`,JSON.stringify(r))}),Object.entries(n).forEach(([r,o])=>{localStorage.setItem(`setting_${r}`,o)}),localStorage.setItem("learning_progress_initialized","true"),console.log("🎭 Mock data initialized")}}async getLearningProgress(t){const n=localStorage.getItem(`progress_${t}`);return n?JSON.parse(n):null}async saveLearningProgress(t){t.lastAccessed=new Date,localStorage.setItem(`progress_${t.filePath}`,JSON.stringify(t))}async getAllLearningProgress(){const t=[];for(let n=0;n<localStorage.length;n++){const r=localStorage.key(n);if(r!=null&&r.startsWith("progress_")){const o=localStorage.getItem(r);o&&t.push(JSON.parse(o))}}return t.sort((n,r)=>new Date(r.lastAccessed).getTime()-new Date(n.lastAccessed).getTime())}async deleteLearningProgress(t){localStorage.removeItem(`progress_${t}`)}async getSetting(t){return localStorage.getItem(`setting_${t}`)}async setSetting(t,n){localStorage.setItem(`setting_${t}`,n)}async getAllSettings(){const t=[];for(let n=0;n<localStorage.length;n++){const r=localStorage.key(n);if(r!=null&&r.startsWith("setting_")){const o=r.replace("setting_",""),l=localStorage.getItem(r);l!==null&&t.push({key:o,value:l})}}return t}async getDirectoryState(t){const n=localStorage.getItem(`directory_${t}`);return n?JSON.parse(n):null}async saveDirectoryState(t){t.lastAccessed=new Date,localStorage.setItem(`directory_${t.path}`,JSON.stringify(t))}async getAllDirectoryStates(){const t=[];for(let n=0;n<localStorage.length;n++){const r=localStorage.key(n);if(r!=null&&r.startsWith("directory_")){const o=localStorage.getItem(r);o&&t.push(JSON.parse(o))}}return t.sort((n,r)=>new Date(r.lastAccessed).getTime()-new Date(n.lastAccessed).getTime())}async deleteDirectoryState(t){localStorage.removeItem(`directory_${t}`)}async getRecentFiles(t=10){return(await this.getAllLearningProgress()).slice(0,t)}async getCompletedFiles(){return(await this.getAllLearningProgress()).filter(n=>n.isCompleted||n.isMarkedAsLearned)}async getInProgressFiles(){return(await this.getAllLearningProgress()).filter(n=>!n.isCompleted&&!n.isMarkedAsLearned&&n.progress>0)}}const G=new r0,Ba=({item:e,onSelect:t,onToggle:n,isSelected:r,isExpanded:o,level:l,progress:s=0,isLearned:i=!1,isDirLearned:a=!1})=>{const c=()=>{e.isDirectory?n(e.path):t(e)},g=W1(e.extension,e.isDirectory,o);return u.jsxs("div",{className:Yt("flex items-center gap-2 px-2 py-1 cursor-pointer hover:bg-accent rounded-sm text-sm",r&&"bg-accent",(i||e.isDirectory&&a)&&"bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-300"),style:{paddingLeft:`${l*16+8}px`},onClick:c,children:[e.isDirectory&&u.jsx("div",{className:"w-4 h-4 flex items-center justify-center",children:o?u.jsx(jm,{className:"w-3 h-3"}):u.jsx(td,{className:"w-3 h-3"})}),u.jsx("div",{className:"w-4 h-4 flex items-center justify-center text-muted-foreground",children:x.createElement(g,{className:"w-3.5 h-3.5"})}),u.jsxs("div",{className:"flex-1 min-w-0",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx("span",{className:"truncate",children:e.name}),(i||e.isDirectory&&a)&&u.jsx("span",{className:"text-xs text-green-600",children:"✓"})]}),!e.isDirectory&&u.jsxs("div",{className:"text-xs text-muted-foreground mt-1",children:[hd(e.size)," • ",Lo(new Date(e.modified))]})]})]})},o0=({currentPath:e,onPathChange:t,onFileSelect:n,selectedFile:r,refreshTrigger:o})=>{const[l,s]=x.useState([]),[i,a]=x.useState(new Set),[c,g]=x.useState(""),[h,p]=x.useState(new Map),[y,k]=x.useState(new Map),[w,E]=x.useState(new Map),[f,d]=x.useState(!1),m=x.useRef(null);x.useEffect(()=>{e&&(z(e),C())},[e]),x.useEffect(()=>{var I;if(o!==void 0){const O=((I=m.current)==null?void 0:I.scrollTop)||0;C().then(()=>{m.current&&(m.current.scrollTop=O)})}},[o]);const S=[".DS_Store","Thumbs.db","desktop.ini",".localized",".fseventsd",".Spotlight-V100",".Trashes",".TemporaryItems",".DocumentRevisions-V100",".VolumeIcon.icns",".com.apple.timemachine.donotpresent",".AppleDouble",".LSOverride",".AppleDB",".AppleDesktop","Network Trash Folder","Temporary Items",".apdisk"],N=I=>I.startsWith(".")?![".gitignore",".env",".env.example",".editorconfig",".prettierrc",".eslintrc"].some(V=>I.startsWith(V)):S.includes(I),z=async I=>{try{d(!0);const V=(await window.electronAPI.readDirectory(I)).filter(j=>!N(j.name));s(V)}catch(O){console.error("Failed to load directory:",O),s([])}finally{d(!1)}},C=async()=>{try{const I=await G.getAllLearningProgress(),O=new Map,V=new Map;I.forEach(j=>{O.set(j.filePath,j.progress),V.set(j.filePath,j.isCompleted||j.isMarkedAsLearned)}),p(O),k(V),await D()}catch(I){console.error("Failed to load progress data:",I)}},D=async()=>{if(e)try{const I=new Map,V=(await window.electronAPI.readDirectory(e)).filter(j=>j.isDirectory);for(const j of V){const T=await A(j.path);I.set(j.path,T)}E(I)}catch(I){console.error("Failed to calculate directory learned status:",I)}},A=async I=>{try{const O=await window.electronAPI.readDirectory(I);let V=!0,j=!1;for(const T of O)T.isDirectory?await A(T.path)||(V=!1):(j=!0,y.get(T.path)||(V=!1));return V}catch(O){return console.error("Failed to check directory learned status:",O),!1}},v=async()=>{try{const I=await window.electronAPI.selectDirectory();I&&(t(I),await G.saveDirectoryState({path:I,lastAccessed:new Date,isBookmarked:!1}),await G.setSetting("lastOpenedDirectory",I))}catch(I){console.error("Failed to select directory:",I)}},_=async I=>{const O=new Set(i);O.has(I)?O.delete(I):O.add(I),a(O)},R=l.filter(I=>I.name.toLowerCase().includes(c.toLowerCase())),H=(I,O=0)=>I.map(V=>{const j=V.isDirectory&&w.get(V.path)||!1;return u.jsxs("div",{children:[u.jsx(Ba,{item:V,onSelect:n,onToggle:_,isSelected:(r==null?void 0:r.path)===V.path,isExpanded:i.has(V.path),level:O,progress:h.get(V.path)||0,isLearned:y.get(V.path)||!1,isDirLearned:j}),V.isDirectory&&i.has(V.path)&&u.jsx(X,{path:V.path,level:O+1})]},V.path)}),X=({path:I,level:O})=>{const[V,j]=x.useState([]);x.useEffect(()=>{(async()=>{try{const W=(await window.electronAPI.readDirectory(I)).filter(Je=>!N(Je.name));j(W)}catch($){console.error("Failed to load subdirectory:",$),j([])}})()},[I]);const T=(F,$)=>F.map(W=>{const Je=W.isDirectory&&w.get(W.path)||!1;return u.jsxs("div",{children:[u.jsx(Ba,{item:W,onSelect:n,onToggle:_,isSelected:(r==null?void 0:r.path)===W.path,isExpanded:i.has(W.path),level:$,progress:h.get(W.path)||0,isLearned:y.get(W.path)||!1,isDirLearned:Je}),W.isDirectory&&i.has(W.path)&&u.jsx(X,{path:W.path,level:$+1})]},W.path)});return u.jsx(u.Fragment,{children:T(V,O)})};return u.jsxs("div",{className:"flex-container-full",children:[u.jsxs("div",{className:"flex-shrink-0 p-4 border-b bg-card select-none cursor-move hover:bg-accent/50 transition-colors",style:{WebkitAppRegion:"drag"},title:"拖动此区域移动窗口",children:[u.jsx("div",{className:"flex items-center justify-end mb-3 cursor-move",style:{WebkitAppRegion:"drag"},children:u.jsxs(Z,{onClick:v,variant:"outline",size:"sm",style:{WebkitAppRegion:"no-drag"},children:[u.jsx(id,{className:"w-4 h-4 mr-2"}),"选择目录"]})}),e&&u.jsx("div",{className:"text-xs text-muted-foreground mb-3 break-all cursor-move hover:bg-accent/30 rounded px-2 py-1 transition-colors",style:{WebkitAppRegion:"drag"},title:"拖动此区域移动窗口",children:e}),u.jsxs("div",{className:"relative",style:{WebkitAppRegion:"no-drag"},children:[u.jsx(rd,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"}),u.jsx(Yo,{placeholder:"搜索文件...",value:c,onChange:I=>g(I.target.value),className:"pl-10"})]})]}),u.jsx("div",{ref:m,className:"flex-1 scrollable-container",children:f?u.jsx("div",{className:"p-4 text-center text-muted-foreground",children:"加载中..."}):e?u.jsx("div",{className:"p-2",children:H(R)}):u.jsx("div",{className:"p-4 text-center text-muted-foreground",children:"请选择一个目录开始浏览"})})]})};function yd(e,[t,n]){return Math.min(n,Math.max(t,e))}function gn(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function xd(e,t=[]){let n=[];function r(l,s){const i=x.createContext(s),a=n.length;n=[...n,s];const c=h=>{var f;const{scope:p,children:y,...k}=h,w=((f=p==null?void 0:p[e])==null?void 0:f[a])||i,E=x.useMemo(()=>k,Object.values(k));return u.jsx(w.Provider,{value:E,children:y})};c.displayName=l+"Provider";function g(h,p){var w;const y=((w=p==null?void 0:p[e])==null?void 0:w[a])||i,k=x.useContext(y);if(k)return k;if(s!==void 0)return s;throw new Error(`\`${h}\` must be used within \`${l}\``)}return[c,g]}const o=()=>{const l=n.map(s=>x.createContext(s));return function(i){const a=(i==null?void 0:i[e])||l;return x.useMemo(()=>({[`__scope${e}`]:{...i,[e]:a}}),[i,a])}};return o.scopeName=e,[r,l0(o,...t)]}function l0(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(l){const s=r.reduce((i,{useScope:a,scopeName:c})=>{const h=a(l)[`__scope${c}`];return{...i,...h}},{});return x.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var wd=globalThis!=null&&globalThis.document?x.useLayoutEffect:()=>{},s0=cf[" useInsertionEffect ".trim().toString()]||wd;function i0({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,l,s]=a0({defaultProp:t,onChange:n}),i=e!==void 0,a=i?e:o;{const g=x.useRef(e!==void 0);x.useEffect(()=>{const h=g.current;h!==i&&console.warn(`${r} is changing from ${h?"controlled":"uncontrolled"} to ${i?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),g.current=i},[i,r])}const c=x.useCallback(g=>{var h;if(i){const p=u0(g)?g(e):g;p!==e&&((h=s.current)==null||h.call(s,p))}else l(g)},[i,e,l,s]);return[a,c]}function a0({defaultProp:e,onChange:t}){const[n,r]=x.useState(e),o=x.useRef(n),l=x.useRef(t);return s0(()=>{l.current=t},[t]),x.useEffect(()=>{var s;o.current!==n&&((s=l.current)==null||s.call(l,n),o.current=n)},[n,o]),[n,r,l]}function u0(e){return typeof e=="function"}var c0=x.createContext(void 0);function d0(e){const t=x.useContext(c0);return e||t||"ltr"}function f0(e){const t=x.useRef({value:e,previous:e});return x.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function p0(e){const[t,n]=x.useState(void 0);return wd(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const l=o[0];let s,i;if("borderBoxSize"in l){const a=l.borderBoxSize,c=Array.isArray(a)?a[0]:a;s=c.inlineSize,i=c.blockSize}else s=e.offsetWidth,i=e.offsetHeight;n({width:s,height:i})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var m0=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Pr=m0.reduce((e,t)=>{const n=To(`Primitive.${t}`),r=x.forwardRef((o,l)=>{const{asChild:s,...i}=o,a=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(a,{...i,ref:l})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function h0(e){const t=e+"CollectionProvider",[n,r]=xd(t),[o,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=w=>{const{scope:E,children:f}=w,d=xe.useRef(null),m=xe.useRef(new Map).current;return u.jsx(o,{scope:E,itemMap:m,collectionRef:d,children:f})};s.displayName=t;const i=e+"CollectionSlot",a=To(i),c=xe.forwardRef((w,E)=>{const{scope:f,children:d}=w,m=l(i,f),S=Rt(E,m.collectionRef);return u.jsx(a,{ref:S,children:d})});c.displayName=i;const g=e+"CollectionItemSlot",h="data-radix-collection-item",p=To(g),y=xe.forwardRef((w,E)=>{const{scope:f,children:d,...m}=w,S=xe.useRef(null),N=Rt(E,S),z=l(g,f);return xe.useEffect(()=>(z.itemMap.set(S,{ref:S,...m}),()=>void z.itemMap.delete(S))),u.jsx(p,{[h]:"",ref:N,children:d})});y.displayName=g;function k(w){const E=l(e+"CollectionConsumer",w);return xe.useCallback(()=>{const d=E.collectionRef.current;if(!d)return[];const m=Array.from(d.querySelectorAll(`[${h}]`));return Array.from(E.itemMap.values()).sort((z,C)=>m.indexOf(z.ref.current)-m.indexOf(C.ref.current))},[E.collectionRef,E.itemMap])}return[{Provider:s,Slot:c,ItemSlot:y},k,r]}var Sd=["PageUp","PageDown"],kd=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],Cd={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},Rn="Slider",[Cs,g0,v0]=h0(Rn),[Nd,G0]=xd(Rn,[v0]),[y0,Jo]=Nd(Rn),jd=x.forwardRef((e,t)=>{const{name:n,min:r=0,max:o=100,step:l=1,orientation:s="horizontal",disabled:i=!1,minStepsBetweenThumbs:a=0,defaultValue:c=[r],value:g,onValueChange:h=()=>{},onValueCommit:p=()=>{},inverted:y=!1,form:k,...w}=e,E=x.useRef(new Set),f=x.useRef(0),m=s==="horizontal"?x0:w0,[S=[],N]=i0({prop:g,defaultProp:c,onChange:_=>{var H;(H=[...E.current][f.current])==null||H.focus(),h(_)}}),z=x.useRef(S);function C(_){const R=j0(S,_);v(_,R)}function D(_){v(_,f.current)}function A(){const _=z.current[f.current];S[f.current]!==_&&p(S)}function v(_,R,{commit:H}={commit:!1}){const X=D0(l),I=_0(Math.round((_-r)/l)*l+r,X),O=yd(I,[r,o]);N((V=[])=>{const j=C0(V,O,R);if(z0(j,a*l)){f.current=j.indexOf(O);const T=String(j)!==String(V);return T&&H&&p(j),T?j:V}else return V})}return u.jsx(y0,{scope:e.__scopeSlider,name:n,disabled:i,min:r,max:o,valueIndexToChangeRef:f,thumbs:E.current,values:S,orientation:s,form:k,children:u.jsx(Cs.Provider,{scope:e.__scopeSlider,children:u.jsx(Cs.Slot,{scope:e.__scopeSlider,children:u.jsx(m,{"aria-disabled":i,"data-disabled":i?"":void 0,...w,ref:t,onPointerDown:gn(w.onPointerDown,()=>{i||(z.current=S)}),min:r,max:o,inverted:y,onSlideStart:i?void 0:C,onSlideMove:i?void 0:D,onSlideEnd:i?void 0:A,onHomeKeyDown:()=>!i&&v(r,0,{commit:!0}),onEndKeyDown:()=>!i&&v(o,S.length-1,{commit:!0}),onStepKeyDown:({event:_,direction:R})=>{if(!i){const I=Sd.includes(_.key)||_.shiftKey&&kd.includes(_.key)?10:1,O=f.current,V=S[O],j=l*I*R;v(V+j,O,{commit:!0})}}})})})})});jd.displayName=Rn;var[Ed,Pd]=Nd(Rn,{startEdge:"left",endEdge:"right",size:"width",direction:1}),x0=x.forwardRef((e,t)=>{const{min:n,max:r,dir:o,inverted:l,onSlideStart:s,onSlideMove:i,onSlideEnd:a,onStepKeyDown:c,...g}=e,[h,p]=x.useState(null),y=Rt(t,m=>p(m)),k=x.useRef(void 0),w=d0(o),E=w==="ltr",f=E&&!l||!E&&l;function d(m){const S=k.current||h.getBoundingClientRect(),N=[0,S.width],C=Si(N,f?[n,r]:[r,n]);return k.current=S,C(m-S.left)}return u.jsx(Ed,{scope:e.__scopeSlider,startEdge:f?"left":"right",endEdge:f?"right":"left",direction:f?1:-1,size:"width",children:u.jsx(zd,{dir:w,"data-orientation":"horizontal",...g,ref:y,style:{...g.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:m=>{const S=d(m.clientX);s==null||s(S)},onSlideMove:m=>{const S=d(m.clientX);i==null||i(S)},onSlideEnd:()=>{k.current=void 0,a==null||a()},onStepKeyDown:m=>{const N=Cd[f?"from-left":"from-right"].includes(m.key);c==null||c({event:m,direction:N?-1:1})}})})}),w0=x.forwardRef((e,t)=>{const{min:n,max:r,inverted:o,onSlideStart:l,onSlideMove:s,onSlideEnd:i,onStepKeyDown:a,...c}=e,g=x.useRef(null),h=Rt(t,g),p=x.useRef(void 0),y=!o;function k(w){const E=p.current||g.current.getBoundingClientRect(),f=[0,E.height],m=Si(f,y?[r,n]:[n,r]);return p.current=E,m(w-E.top)}return u.jsx(Ed,{scope:e.__scopeSlider,startEdge:y?"bottom":"top",endEdge:y?"top":"bottom",size:"height",direction:y?1:-1,children:u.jsx(zd,{"data-orientation":"vertical",...c,ref:h,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:w=>{const E=k(w.clientY);l==null||l(E)},onSlideMove:w=>{const E=k(w.clientY);s==null||s(E)},onSlideEnd:()=>{p.current=void 0,i==null||i()},onStepKeyDown:w=>{const f=Cd[y?"from-bottom":"from-top"].includes(w.key);a==null||a({event:w,direction:f?-1:1})}})})}),zd=x.forwardRef((e,t)=>{const{__scopeSlider:n,onSlideStart:r,onSlideMove:o,onSlideEnd:l,onHomeKeyDown:s,onEndKeyDown:i,onStepKeyDown:a,...c}=e,g=Jo(Rn,n);return u.jsx(Pr.span,{...c,ref:t,onKeyDown:gn(e.onKeyDown,h=>{h.key==="Home"?(s(h),h.preventDefault()):h.key==="End"?(i(h),h.preventDefault()):Sd.concat(kd).includes(h.key)&&(a(h),h.preventDefault())}),onPointerDown:gn(e.onPointerDown,h=>{const p=h.target;p.setPointerCapture(h.pointerId),h.preventDefault(),g.thumbs.has(p)?p.focus():r(h)}),onPointerMove:gn(e.onPointerMove,h=>{h.target.hasPointerCapture(h.pointerId)&&o(h)}),onPointerUp:gn(e.onPointerUp,h=>{const p=h.target;p.hasPointerCapture(h.pointerId)&&(p.releasePointerCapture(h.pointerId),l(h))})})}),Dd="SliderTrack",_d=x.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=Jo(Dd,n);return u.jsx(Pr.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...r,ref:t})});_d.displayName=Dd;var Ns="SliderRange",Md=x.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=Jo(Ns,n),l=Pd(Ns,n),s=x.useRef(null),i=Rt(t,s),a=o.values.length,c=o.values.map(p=>Id(p,o.min,o.max)),g=a>1?Math.min(...c):0,h=100-Math.max(...c);return u.jsx(Pr.span,{"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,...r,ref:i,style:{...e.style,[l.startEdge]:g+"%",[l.endEdge]:h+"%"}})});Md.displayName=Ns;var js="SliderThumb",Ld=x.forwardRef((e,t)=>{const n=g0(e.__scopeSlider),[r,o]=x.useState(null),l=Rt(t,i=>o(i)),s=x.useMemo(()=>r?n().findIndex(i=>i.ref.current===r):-1,[n,r]);return u.jsx(S0,{...e,ref:l,index:s})}),S0=x.forwardRef((e,t)=>{const{__scopeSlider:n,index:r,name:o,...l}=e,s=Jo(js,n),i=Pd(js,n),[a,c]=x.useState(null),g=Rt(t,d=>c(d)),h=a?s.form||!!a.closest("form"):!0,p=p0(a),y=s.values[r],k=y===void 0?0:Id(y,s.min,s.max),w=N0(r,s.values.length),E=p==null?void 0:p[i.size],f=E?E0(E,k,i.direction):0;return x.useEffect(()=>{if(a)return s.thumbs.add(a),()=>{s.thumbs.delete(a)}},[a,s.thumbs]),u.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[i.startEdge]:`calc(${k}% + ${f}px)`},children:[u.jsx(Cs.ItemSlot,{scope:e.__scopeSlider,children:u.jsx(Pr.span,{role:"slider","aria-label":e["aria-label"]||w,"aria-valuemin":s.min,"aria-valuenow":y,"aria-valuemax":s.max,"aria-orientation":s.orientation,"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0,tabIndex:s.disabled?void 0:0,...l,ref:g,style:y===void 0?{display:"none"}:e.style,onFocus:gn(e.onFocus,()=>{s.valueIndexToChangeRef.current=r})})}),h&&u.jsx(Td,{name:o??(s.name?s.name+(s.values.length>1?"[]":""):void 0),form:s.form,value:y},r)]})});Ld.displayName=js;var k0="RadioBubbleInput",Td=x.forwardRef(({__scopeSlider:e,value:t,...n},r)=>{const o=x.useRef(null),l=Rt(o,r),s=f0(t);return x.useEffect(()=>{const i=o.current;if(!i)return;const a=window.HTMLInputElement.prototype,g=Object.getOwnPropertyDescriptor(a,"value").set;if(s!==t&&g){const h=new Event("input",{bubbles:!0});g.call(i,t),i.dispatchEvent(h)}},[s,t]),u.jsx(Pr.input,{style:{display:"none"},...n,ref:l,defaultValue:t})});Td.displayName=k0;function C0(e=[],t,n){const r=[...e];return r[n]=t,r.sort((o,l)=>o-l)}function Id(e,t,n){const l=100/(n-t)*(e-t);return yd(l,[0,100])}function N0(e,t){return t>2?`Value ${e+1} of ${t}`:t===2?["Minimum","Maximum"][e]:void 0}function j0(e,t){if(e.length===1)return 0;const n=e.map(o=>Math.abs(o-t)),r=Math.min(...n);return n.indexOf(r)}function E0(e,t,n){const r=e/2,l=Si([0,50],[0,r]);return(r-l(t)*n)*n}function P0(e){return e.slice(0,-1).map((t,n)=>e[n+1]-t)}function z0(e,t){if(t>0){const n=P0(e);return Math.min(...n)>=t}return!0}function Si(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function D0(e){return(String(e).split(".")[1]||"").length}function _0(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}var Rd=jd,M0=_d,L0=Md,T0=Ld;const Io=x.forwardRef(({className:e,...t},n)=>u.jsxs(Rd,{ref:n,className:Yt("relative flex w-full touch-none select-none items-center",e),...t,children:[u.jsx(M0,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:u.jsx(L0,{className:"absolute h-full bg-primary"})}),u.jsx(T0,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));Io.displayName=Rd.displayName;const I0=({file:e,onProgressUpdate:t})=>{const n=x.useRef(null),[r,o]=x.useState(!1),[l,s]=x.useState(0),[i,a]=x.useState(0),[c,g]=x.useState(1),[h,p]=x.useState(!1),[y,k]=x.useState(1),[w,E]=x.useState(!0),[f,d]=x.useState(!1),[m,S]=x.useState(null),[N,z]=x.useState(!1),C=x.useRef(),D=x.useRef(null),A=x.useRef(0),v=x.useRef(),_=x.useRef(),[R,H]=x.useState(!1);x.useEffect(()=>{X(),I()},[e.path]),x.useEffect(()=>{const L=n.current;if(!L)return;const B=()=>{s(L.currentTime),Je(L.currentTime,L.duration)},ke=()=>{a(L.duration)},zr=()=>{o(!1),V()};return L.addEventListener("timeupdate",B),L.addEventListener("durationchange",ke),L.addEventListener("ended",zr),()=>{L.removeEventListener("timeupdate",B),L.removeEventListener("durationchange",ke),L.removeEventListener("ended",zr)}},[e.path]);const X=async()=>{try{const L=await G.getLearningProgress(e.path);L&&(S(L),n.current&&L.currentPosition&&(n.current.currentTime=L.currentPosition))}catch(L){console.error("Failed to load progress:",L)}},I=async()=>{try{const L=await G.getSetting("videoPlaybackSpeed");if(L){const B=parseFloat(L);k(B),n.current&&(n.current.playbackRate=B)}}catch(L){console.error("Failed to load settings:",L)}},O=async(L,B)=>{if(!B||B===0)return;const ke=L/B*100,zr=ke>=99.5,qo={filePath:e.path,fileType:"video",progress:ke,currentPosition:L,totalDuration:B,isCompleted:zr,isMarkedAsLearned:(m==null?void 0:m.isMarkedAsLearned)||!1,lastAccessed:new Date,notes:(m==null?void 0:m.notes)||""};try{await G.saveLearningProgress(qo),S(qo),t==null||t(qo)}catch(Ud){console.error("Failed to save progress:",Ud)}},V=async()=>{if(!m)return;const L={...m,isCompleted:!0,isMarkedAsLearned:!0,progress:100};try{await G.saveLearningProgress(L),S(L),t==null||t(L)}catch(B){console.error("Failed to mark as completed:",B)}},j=()=>{const L=n.current;L&&(r?L.pause():L.play(),o(!r))},T=L=>{const B=n.current;if(!B||!i)return;const ke=L[0]/100*i;B.currentTime=ke,s(ke)},F=L=>{const B=n.current;if(!B)return;const ke=L[0]/100;B.volume=ke,g(ke),p(ke===0)},$=()=>{const L=n.current;L&&(h?(L.volume=c,p(!1)):(L.volume=0,p(!0)))},W=async L=>{const B=n.current;if(B){B.playbackRate=L,k(L);try{await G.setSetting("videoPlaybackSpeed",L.toString())}catch(ke){console.error("Failed to save playback speed:",ke)}}},Je=(L,B)=>{v.current&&clearTimeout(v.current),!(Math.abs(L-A.current)<5&&r)&&(v.current=setTimeout(()=>{O(L,B),A.current=L},r?2e3:500))};x.useEffect(()=>()=>{v.current&&clearTimeout(v.current),C.current&&clearTimeout(C.current),_.current&&clearTimeout(_.current),n.current&&n.current.duration&&O(n.current.currentTime,n.current.duration)},[e.path]),x.useEffect(()=>{!r&&n.current&&n.current.duration&&(v.current&&clearTimeout(v.current),O(n.current.currentTime,n.current.duration),A.current=n.current.currentTime)},[r]);const Oe=L=>{const B=n.current;B&&(B.currentTime=Math.max(0,Math.min(B.currentTime+L,i)))},en=()=>{const L=n.current;L&&(f?document.exitFullscreen&&document.exitFullscreen():L.requestFullscreen&&L.requestFullscreen(),d(!f))},ot=()=>{C.current&&clearTimeout(C.current),r&&(C.current=setTimeout(()=>{E(!1)},3e3))},tn=()=>{E(!0),H(!0),_.current&&clearTimeout(_.current),C.current&&clearTimeout(C.current),_.current=setTimeout(()=>{H(!1),ot()},500)},Od=()=>{z(!0)},Vd=()=>{z(!1),E(!0)},bd=()=>{E(!0),H(!0),C.current&&clearTimeout(C.current)},$d=()=>{H(!1),_.current&&clearTimeout(_.current),C.current&&clearTimeout(C.current),r&&(C.current=setTimeout(()=>{E(!1)},1e3))};x.useEffect(()=>{const L=B=>{if(N)switch(B.code){case"Space":B.preventDefault(),j();break;case"ArrowLeft":B.preventDefault(),Oe(-10);break;case"ArrowRight":B.preventDefault(),Oe(10);break;case"ArrowUp":B.preventDefault(),F([Math.min(100,c*100+10)]);break;case"ArrowDown":B.preventDefault(),F([Math.max(0,c*100-10)]);break;case"KeyM":B.preventDefault(),$();break;case"KeyF":B.preventDefault(),en();break}};return document.addEventListener("keydown",L),()=>document.removeEventListener("keydown",L)},[N,c,r]);const Bd=i?l/i*100:0;return u.jsx("div",{className:"flex-container-full",children:u.jsxs("div",{ref:D,className:"relative flex-1 bg-black group",onMouseEnter:bd,onMouseLeave:$d,onMouseMove:tn,tabIndex:0,onFocus:Od,onBlur:Vd,children:[u.jsx("video",{ref:n,src:(()=>{var B;return typeof window<"u"&&!((B=window.electronAPI)!=null&&B.readDirectory)?"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4":`file://${e.path}`})(),className:"w-full h-full object-contain",onClick:j,onDoubleClick:j,crossOrigin:"anonymous"}),u.jsxs("div",{className:Yt("absolute inset-0 transition-opacity duration-300",w||!r?"opacity-100":"opacity-0"),children:[u.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-black/70 to-transparent"}),u.jsxs("div",{className:"absolute bottom-16 left-4 right-4 z-10",children:[u.jsx(Io,{value:[Bd],onValueChange:T,max:100,step:.1,className:"w-full"}),u.jsxs("div",{className:"flex justify-between text-xs text-white mt-1",children:[u.jsx("span",{children:Aa(l)}),u.jsx("span",{children:Aa(i)})]})]}),u.jsxs("div",{className:"absolute bottom-4 left-4 right-4 flex items-center justify-between z-10",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(Z,{variant:"ghost",size:"icon",onClick:()=>Oe(-10),className:"text-white hover:bg-white/20",children:u.jsx(Tm,{className:"w-5 h-5"})}),u.jsx(Z,{variant:"ghost",size:"icon",onClick:j,className:"text-white hover:bg-white/20",children:r?u.jsx(_m,{className:"w-6 h-6"}):u.jsx(Mm,{className:"w-6 h-6"})}),u.jsx(Z,{variant:"ghost",size:"icon",onClick:()=>Oe(10),className:"text-white hover:bg-white/20",children:u.jsx(Im,{className:"w-5 h-5"})}),u.jsxs("div",{className:"flex items-center gap-2 ml-4",children:[u.jsx(Z,{variant:"ghost",size:"icon",onClick:$,className:"text-white hover:bg-white/20",children:h?u.jsx(Am,{className:"w-5 h-5"}):u.jsx(Fm,{className:"w-5 h-5"})}),u.jsx(Io,{value:[h?0:c*100],onValueChange:F,max:100,className:"w-20"})]})]}),u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsxs("select",{value:y,onChange:L=>W(parseFloat(L.target.value)),className:"bg-black/50 text-white border border-white/20 rounded px-2 py-1 text-sm",children:[u.jsx("option",{value:.5,children:"0.5x"}),u.jsx("option",{value:.75,children:"0.75x"}),u.jsx("option",{value:1,children:"1x"}),u.jsx("option",{value:1.25,children:"1.25x"}),u.jsx("option",{value:1.5,children:"1.5x"}),u.jsx("option",{value:2,children:"2x"})]}),u.jsx(Z,{variant:"ghost",size:"icon",onClick:en,className:"text-white hover:bg-white/20",children:u.jsx(Dm,{className:"w-5 h-5"})})]})]})]}),i===0&&u.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-black/50",children:u.jsx("div",{className:"text-white",children:"加载中..."})})]})})},R0=({file:e,onProgressUpdate:t})=>{const[n,r]=x.useState(null);x.useEffect(()=>{o(),l()},[e.path]);const o=async()=>{try{const i=await G.getLearningProgress(e.path);i&&r(i)}catch(i){console.error("Failed to load progress:",i)}},l=async()=>{const i={filePath:e.path,fileType:"pdf",progress:100,isCompleted:!0,isMarkedAsLearned:(n==null?void 0:n.isMarkedAsLearned)||!1,lastAccessed:new Date,notes:(n==null?void 0:n.notes)||""};try{await G.saveLearningProgress(i),r(i),t==null||t(i)}catch(a){console.error("Failed to save progress:",a)}},s=()=>{var a;return typeof window<"u"&&!((a=window.electronAPI)!=null&&a.readDirectory)?"https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf":`file://${e.path}`};return u.jsx("div",{className:"flex-container-full",children:u.jsx("div",{className:"flex-1 scrollable-container bg-gray-100",children:u.jsx("div",{className:"p-4 pt-8 h-full",children:u.jsx("iframe",{src:s(),className:"w-full h-full border-0 rounded shadow-lg",title:`PDF: ${e.name}`,style:{minHeight:"calc(100vh - 200px)"}})})})})},F0=({file:e,onProgressUpdate:t})=>{const[n,r]=x.useState("auto"),[o,l]=x.useState(0),[s,i]=x.useState(null),[a,c]=x.useState(!1),[g,h]=x.useState(!1),[p,y]=x.useState(1);x.useEffect(()=>{k(),w()},[e.path]);const k=async()=>{try{const C=await G.getLearningProgress(e.path);C&&i(C)}catch(C){console.error("Failed to load progress:",C)}},w=async()=>{const C={filePath:e.path,fileType:"image",progress:100,currentPosition:1,totalDuration:1,isCompleted:!0,isMarkedAsLearned:(s==null?void 0:s.isMarkedAsLearned)||!1,lastAccessed:new Date,notes:(s==null?void 0:s.notes)||""};try{await G.saveLearningProgress(C),i(C),t==null||t(C)}catch(D){console.error("Failed to save progress:",D)}},E=async()=>{if(!s)return;const C={...s,isMarkedAsLearned:!0};try{await G.saveLearningProgress(C),i(C),t==null||t(C)}catch(D){console.error("Failed to mark as learned:",D)}},f=()=>{r(C=>Math.min(5,(typeof C=="number"?C:p)+.2))},d=()=>{r(C=>Math.max(.1,(typeof C=="number"?C:p)-.2))},m=()=>{r("auto")},S=()=>{l(C=>(C+90)%360)},N=async()=>{try{await window.electronAPI.openExternal(`file://${e.path}`)}catch(C){console.error("Failed to open external:",C)}},z=async()=>{try{await window.electronAPI.showItemInFolder(e.path)}catch(C){console.error("Failed to show in folder:",C)}};return g?u.jsx("div",{className:"h-full flex items-center justify-center",children:u.jsxs("div",{className:"text-center text-muted-foreground",children:[u.jsx("div",{className:"text-4xl mb-4",children:"🖼️"}),u.jsx("div",{className:"mb-2",children:"无法加载图片"}),u.jsx("div",{className:"text-sm",children:e.name})]})}):u.jsxs("div",{className:"flex-container-full",children:[u.jsxs("div",{className:"flex-shrink-0 flex items-center justify-between p-4 border-b bg-background",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(Z,{variant:"outline",size:"sm",onClick:d,children:u.jsx(bm,{className:"w-4 h-4"})}),u.jsx("span",{className:"text-sm text-muted-foreground min-w-[60px] text-center",children:n==="auto"?"Auto":`${Math.round((typeof n=="number"?n:p)*100)}%`}),u.jsx(Z,{variant:"outline",size:"sm",onClick:f,children:u.jsx(Vm,{className:"w-4 h-4"})}),u.jsx(Z,{variant:"outline",size:"sm",onClick:m,children:"重置"}),u.jsx(Z,{variant:"outline",size:"sm",onClick:S,children:u.jsx(Lm,{className:"w-4 h-4"})})]}),u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsxs(Z,{variant:"outline",size:"sm",onClick:z,children:[u.jsx(nd,{className:"w-4 h-4 mr-2"}),"在文件夹中显示"]}),u.jsxs(Z,{variant:"outline",size:"sm",onClick:N,children:[u.jsx(zm,{className:"w-4 h-4 mr-2"}),"用外部程序打开"]}),s&&!s.isMarkedAsLearned&&u.jsx(Z,{variant:"default",size:"sm",onClick:E,children:"标记为已学"})]})]}),u.jsx("div",{className:"flex-1 scrollable-container bg-gray-100",children:u.jsx("div",{className:"p-4 pt-8",style:{minHeight:"100%",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:n==="auto"&&p<1?"center":"flex-start"},children:u.jsx("img",{src:(()=>{var D;return typeof window<"u"&&!((D=window.electronAPI)!=null&&D.readDirectory)?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vZGUuanMg5p625p6E5Zu+PC90ZXh0Pgo8L3N2Zz4K":`file://${e.path}`})(),alt:e.name,style:{transform:n==="auto"?`rotate(${o}deg)`:`scale(${n}) rotate(${o}deg)`,transition:"transform 0.2s ease-in-out",maxWidth:n==="auto"?"100%":"none",maxHeight:n==="auto"?"100%":"none",objectFit:n==="auto"?"contain":"initial",display:"block"},onLoad:C=>{var D;if(c(!0),n==="auto"){const A=C.target,v=(D=A.parentElement)==null?void 0:D.parentElement;if(v){const _=v.clientWidth-32,R=v.clientHeight-32,H=_/A.naturalWidth,X=R/A.naturalHeight;y(Math.min(H,X,1))}}},onError:()=>h(!0),className:"shadow-lg"})})})]})},A0=({file:e,onProgressUpdate:t})=>{const[n,r]=x.useState(""),[o,l]=x.useState(!0),[s,i]=x.useState(null),[a,c]=x.useState(14),[g,h]=x.useState(""),[p,y]=x.useState(null);x.useEffect(()=>{k(),w()},[e.path]);const k=async()=>{var N;try{if(l(!0),i(null),typeof window<"u"&&!((N=window.electronAPI)!=null&&N.readDirectory)){const{sampleTextContent:C}=await vd(async()=>{const{sampleTextContent:D}=await import("./sampleContent-BpF-lE46.js");return{sampleTextContent:D}},[],import.meta.url);r(C)}else{const C=await fetch(`file://${e.path}`);if(!C.ok)throw new Error("Failed to load file");const D=await C.text();r(D)}E()}catch(z){console.error("Failed to load file:",z),i("无法加载文件内容")}finally{l(!1)}},w=async()=>{try{const N=await G.getLearningProgress(e.path);N&&y(N)}catch(N){console.error("Failed to load progress:",N)}},E=async()=>{const N={filePath:e.path,fileType:"text",progress:100,currentPosition:1,totalDuration:1,isCompleted:!0,isMarkedAsLearned:(p==null?void 0:p.isMarkedAsLearned)||!1,lastAccessed:new Date,notes:(p==null?void 0:p.notes)||""};try{await G.saveLearningProgress(N),y(N),t==null||t(N)}catch(z){console.error("Failed to save progress:",z)}},f=async()=>{if(!p)return;const N={...p,isMarkedAsLearned:!0};try{await G.saveLearningProgress(N),y(N),t==null||t(N)}catch(z){console.error("Failed to mark as learned:",z)}},d=async()=>{try{await window.electronAPI.openExternal(`file://${e.path}`)}catch(N){console.error("Failed to open external:",N)}},m=async()=>{try{await window.electronAPI.showItemInFolder(e.path)}catch(N){console.error("Failed to show in folder:",N)}},S=(N,z)=>{if(!z)return N;const C=new RegExp(`(${z})`,"gi");return N.replace(C,'<mark class="bg-yellow-200">$1</mark>')};return o?u.jsx("div",{className:"h-full flex items-center justify-center",children:u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"}),u.jsx("div",{children:"加载文件中..."})]})}):s?u.jsx("div",{className:"h-full flex items-center justify-center",children:u.jsxs("div",{className:"text-center text-red-500",children:[u.jsx("div",{className:"text-4xl mb-4",children:"📄"}),u.jsx("div",{className:"mb-2",children:s}),u.jsx("div",{className:"text-sm",children:e.name})]})}):u.jsxs("div",{className:"flex-container-full",children:[u.jsxs("div",{className:"flex-shrink-0 flex items-center justify-between p-4 border-b bg-background",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(Rm,{className:"w-4 h-4"}),u.jsx("input",{type:"range",min:"10",max:"24",value:a,onChange:N=>c(parseInt(N.target.value)),className:"w-20"}),u.jsxs("span",{className:"text-sm text-muted-foreground min-w-[40px]",children:[a,"px"]})]}),u.jsxs("div",{className:"relative ml-4",children:[u.jsx(rd,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"}),u.jsx(Yo,{placeholder:"搜索文本...",value:g,onChange:N=>h(N.target.value),className:"pl-10 w-48"})]})]}),u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsxs(Z,{variant:"outline",size:"sm",onClick:m,children:[u.jsx(nd,{className:"w-4 h-4 mr-2"}),"在文件夹中显示"]}),u.jsx(Z,{variant:"outline",size:"sm",onClick:d,children:"用外部程序打开"}),p&&!p.isMarkedAsLearned&&u.jsx(Z,{variant:"default",size:"sm",onClick:f,children:"标记为已学"})]})]}),u.jsx("div",{className:"flex-1 scrollable-container p-4 pt-8 bg-background",children:u.jsx("pre",{className:"whitespace-pre-wrap font-mono leading-relaxed",style:{fontSize:`${a}px`},dangerouslySetInnerHTML:{__html:S(n,g)}})}),u.jsx("div",{className:"flex-shrink-0 p-4 border-t bg-background",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("div",{className:"font-medium",children:e.name}),u.jsxs("div",{className:"text-sm text-muted-foreground",children:[hd(e.size)," • ",Lo(new Date(e.modified))," • ",n.split(`
`).length," 行"]})]}),(p==null?void 0:p.isMarkedAsLearned)&&u.jsx("span",{className:"text-green-600 text-sm",children:"✓ 已学习"})]})})]})},O0=({isOpen:e,onClose:t,onSettingsChange:n})=>{const[r,o]=x.useState("settings"),[l,s]=x.useState(1),[i,a]=x.useState(!0),[c,g]=x.useState([]),[h,p]=x.useState([]),[y,k]=x.useState([]),[w,E]=x.useState("");x.useEffect(()=>{e&&(f(),d())},[e]);const f=async()=>{try{const v=await G.getSetting("videoPlaybackSpeed"),_=await G.getSetting("autoMarkAsLearned");s(v?parseFloat(v):1),a(_!=="false")}catch(v){console.error("Failed to load settings:",v)}},d=async()=>{try{const v=await G.getRecentFiles(20),_=await G.getCompletedFiles(),R=await G.getInProgressFiles();g(v),p(_),k(R)}catch(v){console.error("Failed to load progress data:",v)}},m=async v=>{try{await G.setSetting("videoPlaybackSpeed",v.toString()),s(v),n==null||n()}catch(_){console.error("Failed to save playback speed:",_)}},S=async v=>{try{await G.setSetting("autoMarkAsLearned",v.toString()),a(v),n==null||n()}catch(_){console.error("Failed to save auto mark setting:",_)}},N=async v=>{try{await G.deleteLearningProgress(v),await d()}catch(_){console.error("Failed to delete progress:",_)}},z=async v=>{try{const _={...v,isMarkedAsLearned:!v.isMarkedAsLearned};await G.saveLearningProgress(_),await d()}catch(_){console.error("Failed to toggle learned status:",_)}},C=v=>w?v.filter(_=>_.filePath.toLowerCase().includes(w.toLowerCase())):v,D=v=>{switch(v){case"video":return"🎬";case"pdf":return"📕";case"image":return"🖼️";case"text":return"📝";default:return"📄"}},A=v=>v.split("/").pop()||v;return e?u.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:u.jsxs("div",{className:"bg-background border rounded-lg w-[800px] h-[600px] flex flex-col",children:[u.jsxs("div",{className:"flex items-center justify-between p-4 border-b",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(od,{className:"w-5 h-5"}),u.jsx("h2",{className:"text-lg font-semibold",children:"设置与进度管理"})]}),u.jsx(Z,{variant:"ghost",size:"icon",onClick:t,children:u.jsx(Om,{className:"w-4 h-4"})})]}),u.jsxs("div",{className:"flex border-b",children:[u.jsx("button",{className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${r==="settings"?"border-primary text-primary":"border-transparent text-muted-foreground hover:text-foreground"}`,onClick:()=>o("settings"),children:"全局设置"}),u.jsx("button",{className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${r==="progress"?"border-primary text-primary":"border-transparent text-muted-foreground hover:text-foreground"}`,onClick:()=>o("progress"),children:"学习进度"}),u.jsx("button",{className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${r==="stats"?"border-primary text-primary":"border-transparent text-muted-foreground hover:text-foreground"}`,onClick:()=>o("stats"),children:"学习统计"})]}),u.jsxs("div",{className:"flex-1 overflow-auto p-4",children:[r==="settings"&&u.jsxs("div",{className:"space-y-6",children:[u.jsxs("div",{children:[u.jsx("h3",{className:"text-sm font-medium mb-3",children:"视频播放设置"}),u.jsx("div",{className:"space-y-4",children:u.jsxs("div",{children:[u.jsxs("label",{className:"text-sm text-muted-foreground",children:["默认播放倍速: ",l,"x"]}),u.jsx(Io,{value:[l],onValueChange:v=>m(v[0]),min:.5,max:2,step:.25,className:"mt-2"}),u.jsxs("div",{className:"flex justify-between text-xs text-muted-foreground mt-1",children:[u.jsx("span",{children:"0.5x"}),u.jsx("span",{children:"2x"})]})]})})]}),u.jsxs("div",{children:[u.jsx("h3",{className:"text-sm font-medium mb-3",children:"学习行为设置"}),u.jsx("div",{className:"space-y-4",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("div",{className:"text-sm",children:"自动标记为已学"}),u.jsx("div",{className:"text-xs text-muted-foreground",children:"当视频播放完成或PDF阅读到95%时自动标记为已学"})]}),u.jsx("button",{className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${i?"bg-primary":"bg-muted"}`,onClick:()=>S(!i),children:u.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${i?"translate-x-6":"translate-x-1"}`})})]})})]})]}),r==="progress"&&u.jsxs("div",{className:"space-y-4",children:[u.jsx("div",{className:"flex items-center gap-2",children:u.jsx(Yo,{placeholder:"搜索文件...",value:w,onChange:v=>E(v.target.value),className:"flex-1"})}),u.jsxs("div",{className:"space-y-4",children:[u.jsxs("div",{children:[u.jsxs("h3",{className:"text-sm font-medium mb-2 flex items-center gap-2",children:[u.jsx(Pm,{className:"w-4 h-4"}),"最近学习 (",c.length,")"]}),u.jsx("div",{className:"space-y-2 max-h-32 overflow-auto",children:C(c).map(v=>u.jsxs("div",{className:"flex items-center justify-between p-2 border rounded",children:[u.jsxs("div",{className:"flex items-center gap-2 flex-1 min-w-0",children:[u.jsx("span",{children:D(v.fileType)}),u.jsxs("div",{className:"min-w-0 flex-1",children:[u.jsx("div",{className:"text-sm truncate",children:A(v.filePath)}),u.jsxs("div",{className:"text-xs text-muted-foreground",children:[Lo(new Date(v.lastAccessed))," • ",Math.round(v.progress),"%"]})]})]}),u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsx(Z,{variant:"ghost",size:"icon",onClick:()=>z(v),className:"h-8 w-8",children:u.jsx(Qr,{className:`w-4 h-4 ${v.isMarkedAsLearned?"text-green-600":"text-muted-foreground"}`})}),u.jsx(Z,{variant:"ghost",size:"icon",onClick:()=>N(v.filePath),className:"h-8 w-8 text-red-500 hover:text-red-700",children:u.jsx(Pl,{className:"w-4 h-4"})})]})]},v.filePath))})]}),u.jsxs("div",{children:[u.jsxs("h3",{className:"text-sm font-medium mb-2 flex items-center gap-2",children:[u.jsx(Nm,{className:"w-4 h-4"}),"进行中 (",y.length,")"]}),u.jsx("div",{className:"space-y-2 max-h-32 overflow-auto",children:C(y).map(v=>u.jsxs("div",{className:"flex items-center justify-between p-2 border rounded",children:[u.jsxs("div",{className:"flex items-center gap-2 flex-1 min-w-0",children:[u.jsx("span",{children:D(v.fileType)}),u.jsxs("div",{className:"min-w-0 flex-1",children:[u.jsx("div",{className:"text-sm truncate",children:A(v.filePath)}),u.jsxs("div",{className:"text-xs text-muted-foreground",children:["进度: ",Math.round(v.progress),"%"]})]})]}),u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsx(Z,{variant:"ghost",size:"icon",onClick:()=>z(v),className:"h-8 w-8",children:u.jsx(Qr,{className:"w-4 h-4 text-muted-foreground"})}),u.jsx(Z,{variant:"ghost",size:"icon",onClick:()=>N(v.filePath),className:"h-8 w-8 text-red-500 hover:text-red-700",children:u.jsx(Pl,{className:"w-4 h-4"})})]})]},v.filePath))})]}),u.jsxs("div",{children:[u.jsxs("h3",{className:"text-sm font-medium mb-2 flex items-center gap-2",children:[u.jsx(Qr,{className:"w-4 h-4 text-green-600"}),"已完成 (",h.length,")"]}),u.jsx("div",{className:"space-y-2 max-h-32 overflow-auto",children:C(h).map(v=>u.jsxs("div",{className:"flex items-center justify-between p-2 border rounded",children:[u.jsxs("div",{className:"flex items-center gap-2 flex-1 min-w-0",children:[u.jsx("span",{children:D(v.fileType)}),u.jsxs("div",{className:"min-w-0 flex-1",children:[u.jsx("div",{className:"text-sm truncate",children:A(v.filePath)}),u.jsx("div",{className:"text-xs text-muted-foreground",children:Lo(new Date(v.lastAccessed))})]})]}),u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsx(Qr,{className:"w-4 h-4 text-green-600"}),u.jsx(Z,{variant:"ghost",size:"icon",onClick:()=>N(v.filePath),className:"h-8 w-8 text-red-500 hover:text-red-700",children:u.jsx(Pl,{className:"w-4 h-4"})})]})]},v.filePath))})]})]})]}),r==="stats"&&u.jsxs("div",{className:"space-y-6",children:[u.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[u.jsxs("div",{className:"text-center p-4 border rounded",children:[u.jsx("div",{className:"text-2xl font-bold text-primary",children:c.length}),u.jsx("div",{className:"text-sm text-muted-foreground",children:"总学习文件"})]}),u.jsxs("div",{className:"text-center p-4 border rounded",children:[u.jsx("div",{className:"text-2xl font-bold text-green-600",children:h.length}),u.jsx("div",{className:"text-sm text-muted-foreground",children:"已完成"})]}),u.jsxs("div",{className:"text-center p-4 border rounded",children:[u.jsx("div",{className:"text-2xl font-bold text-orange-600",children:y.length}),u.jsx("div",{className:"text-sm text-muted-foreground",children:"进行中"})]})]}),u.jsxs("div",{children:[u.jsx("h3",{className:"text-sm font-medium mb-3",children:"文件类型分布"}),u.jsx("div",{className:"space-y-2",children:["video","pdf","image","text"].map(v=>{const _=c.filter(H=>H.fileType===v).length,R=c.length>0?_/c.length*100:0;return u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx("span",{className:"w-12 text-sm",children:D(v)}),u.jsx("div",{className:"flex-1 bg-secondary rounded-full h-2",children:u.jsx("div",{className:"bg-primary h-2 rounded-full transition-all",style:{width:`${R}%`}})}),u.jsx("span",{className:"text-sm text-muted-foreground w-12 text-right",children:_})]},v)})})]})]})]})]})}):null},V0=({leftPanel:e,rightPanel:t,defaultWidth:n=320,minWidth:r=200,maxWidth:o=600,className:l})=>{const[s,i]=x.useState(n),[a,c]=x.useState(!1),g=x.useRef(null),h=x.useCallback(k=>{k.preventDefault(),c(!0)},[]),p=x.useCallback(k=>{if(!a||!g.current)return;const w=g.current.getBoundingClientRect(),E=k.clientX-w.left,f=Math.max(r,Math.min(o,E));i(f)},[a,r,o]),y=x.useCallback(()=>{c(!1)},[]);return x.useEffect(()=>(a?(document.addEventListener("mousemove",p),document.addEventListener("mouseup",y),document.body.style.cursor="col-resize",document.body.style.userSelect="none"):(document.removeEventListener("mousemove",p),document.removeEventListener("mouseup",y),document.body.style.cursor="",document.body.style.userSelect=""),()=>{document.removeEventListener("mousemove",p),document.removeEventListener("mouseup",y),document.body.style.cursor="",document.body.style.userSelect=""}),[a,p,y]),u.jsxs("div",{ref:g,className:Yt("flex h-full",l),children:[u.jsx("div",{className:"flex-shrink-0 border-r bg-card h-full",style:{width:s},children:e}),u.jsx("div",{className:Yt("w-1 bg-border hover:bg-primary/50 cursor-col-resize transition-colors relative group",a&&"bg-primary"),onMouseDown:h,children:u.jsx("div",{className:"absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-0.5 bg-border group-hover:bg-primary/50 transition-colors"})}),u.jsx("div",{className:"flex-1 min-w-0 h-full",children:t})]})},b0=({onPrevious:e,onNext:t,hasPrevious:n,hasNext:r})=>u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsxs(Z,{variant:"ghost",size:"sm",onClick:e,disabled:!n,className:"h-8 px-2",children:[u.jsx(Em,{className:"w-4 h-4"}),u.jsx("span",{className:"ml-1 text-xs",children:"上一个"})]}),u.jsxs(Z,{variant:"ghost",size:"sm",onClick:t,disabled:!r,className:"h-8 px-2",children:[u.jsx("span",{className:"mr-1 text-xs",children:"下一个"}),u.jsx(td,{className:"w-4 h-4"})]})]}),Fd=[{name:"JavaScript基础教程.pdf",path:"/Users/<USER>/Documents/学习资料/JavaScript基础教程.pdf",size:2048576,modified:Date.now()-864e5,isDirectory:!1,extension:".pdf"},{name:"React入门视频.mp4",path:"/Users/<USER>/Documents/学习资料/React入门视频.mp4",size:104857600,modified:Date.now()-1728e5,isDirectory:!1,extension:".mp4"},{name:"TypeScript文档.md",path:"/Users/<USER>/Documents/学习资料/TypeScript文档.md",size:51200,modified:Date.now()-2592e5,isDirectory:!1,extension:".md"},{name:"Node.js架构图.png",path:"/Users/<USER>/Documents/学习资料/Node.js架构图.png",size:1024e3,modified:Date.now()-3456e5,isDirectory:!1,extension:".png"},{name:"前端开发",path:"/Users/<USER>/Documents/学习资料/前端开发",size:0,modified:Date.now()-432e6,isDirectory:!0,extension:""},{name:"后端开发",path:"/Users/<USER>/Documents/学习资料/后端开发",size:0,modified:Date.now()-5184e5,isDirectory:!0,extension:""},{name:"Vue.js完整教程.pdf",path:"/Users/<USER>/Documents/学习资料/Vue.js完整教程.pdf",size:3145728,modified:Date.now()-6048e5,isDirectory:!1,extension:".pdf"},{name:"Angular开发指南.pdf",path:"/Users/<USER>/Documents/学习资料/Angular开发指南.pdf",size:2621440,modified:Date.now()-6912e5,isDirectory:!1,extension:".pdf"},{name:"Python数据分析.mp4",path:"/Users/<USER>/Documents/学习资料/Python数据分析.mp4",size:157286400,modified:Date.now()-7776e5,isDirectory:!1,extension:".mp4"},{name:"Docker容器化部署.mp4",path:"/Users/<USER>/Documents/学习资料/Docker容器化部署.mp4",size:209715200,modified:Date.now()-864e6,isDirectory:!1,extension:".mp4"},{name:"Kubernetes集群管理.pdf",path:"/Users/<USER>/Documents/学习资料/Kubernetes集群管理.pdf",size:4194304,modified:Date.now()-9504e5,isDirectory:!1,extension:".pdf"},{name:"Git版本控制.md",path:"/Users/<USER>/Documents/学习资料/Git版本控制.md",size:76800,modified:Date.now()-10368e5,isDirectory:!1,extension:".md"},{name:"Linux系统管理.md",path:"/Users/<USER>/Documents/学习资料/Linux系统管理.md",size:102400,modified:Date.now()-11232e5,isDirectory:!1,extension:".md"},{name:"MySQL数据库设计.pdf",path:"/Users/<USER>/Documents/学习资料/MySQL数据库设计.pdf",size:1572864,modified:Date.now()-12096e5,isDirectory:!1,extension:".pdf"},{name:"Redis缓存优化.md",path:"/Users/<USER>/Documents/学习资料/Redis缓存优化.md",size:61440,modified:Date.now()-1296e6,isDirectory:!1,extension:".md"},{name:"Nginx配置详解.md",path:"/Users/<USER>/Documents/学习资料/Nginx配置详解.md",size:40960,modified:Date.now()-13824e5,isDirectory:!1,extension:".md"},{name:"AWS云服务架构.png",path:"/Users/<USER>/Documents/学习资料/AWS云服务架构.png",size:2048e3,modified:Date.now()-14688e5,isDirectory:!1,extension:".png"},{name:"微服务架构设计.png",path:"/Users/<USER>/Documents/学习资料/微服务架构设计.png",size:1536e3,modified:Date.now()-15552e5,isDirectory:!1,extension:".png"},{name:"移动开发",path:"/Users/<USER>/Documents/学习资料/移动开发",size:0,modified:Date.now()-16416e5,isDirectory:!0,extension:""},{name:"数据科学",path:"/Users/<USER>/Documents/学习资料/数据科学",size:0,modified:Date.now()-1728e6,isDirectory:!0,extension:""}],Ad={"/Users/<USER>/Documents/学习资料/前端开发":[{name:"HTML5新特性.pdf",path:"/Users/<USER>/Documents/学习资料/前端开发/HTML5新特性.pdf",size:1536e3,modified:Date.now()-6048e5,isDirectory:!1,extension:".pdf"},{name:"CSS3动画教程.mp4",path:"/Users/<USER>/Documents/学习资料/前端开发/CSS3动画教程.mp4",size:83886080,modified:Date.now()-6912e5,isDirectory:!1,extension:".mp4"},{name:"Vue.js实战项目",path:"/Users/<USER>/Documents/学习资料/前端开发/Vue.js实战项目",size:0,modified:Date.now()-7776e5,isDirectory:!0,extension:""}],"/Users/<USER>/Documents/学习资料/后端开发":[{name:"Express框架教程.pdf",path:"/Users/<USER>/Documents/学习资料/后端开发/Express框架教程.pdf",size:2097152,modified:Date.now()-864e6,isDirectory:!1,extension:".pdf"},{name:"MongoDB数据库.mp4",path:"/Users/<USER>/Documents/学习资料/后端开发/MongoDB数据库.mp4",size:125829120,modified:Date.now()-9504e5,isDirectory:!1,extension:".mp4"}],"/Users/<USER>/Documents/学习资料/前端开发/Vue.js实战项目":[{name:"项目搭建.md",path:"/Users/<USER>/Documents/学习资料/前端开发/Vue.js实战项目/项目搭建.md",size:25600,modified:Date.now()-10368e5,isDirectory:!1,extension:".md"},{name:"组件开发.md",path:"/Users/<USER>/Documents/学习资料/前端开发/Vue.js实战项目/组件开发.md",size:38400,modified:Date.now()-11232e5,isDirectory:!1,extension:".md"}]},$0=[{filePath:"/Users/<USER>/Documents/学习资料/JavaScript基础教程.pdf",fileType:"pdf",progress:75,currentPosition:15,totalDuration:20,isCompleted:!1,isMarkedAsLearned:!1,lastAccessed:new Date(Date.now()-36e5),notes:"学习了变量和函数部分"},{filePath:"/Users/<USER>/Documents/学习资料/React入门视频.mp4",fileType:"video",progress:100,currentPosition:3600,totalDuration:3600,isCompleted:!0,isMarkedAsLearned:!0,lastAccessed:new Date(Date.now()-72e5),notes:"已完成学习，掌握了基本概念"},{filePath:"/Users/<USER>/Documents/学习资料/TypeScript文档.md",fileType:"text",progress:50,currentPosition:1,totalDuration:1,isCompleted:!1,isMarkedAsLearned:!1,lastAccessed:new Date(Date.now()-108e5),notes:"正在学习类型系统"},{filePath:"/Users/<USER>/Documents/学习资料/Node.js架构图.png",fileType:"image",progress:100,currentPosition:1,totalDuration:1,isCompleted:!0,isMarkedAsLearned:!0,lastAccessed:new Date(Date.now()-144e5),notes:"已查看"},{filePath:"/Users/<USER>/Documents/学习资料/前端开发/HTML5新特性.pdf",fileType:"pdf",progress:30,currentPosition:3,totalDuration:10,isCompleted:!1,isMarkedAsLearned:!1,lastAccessed:new Date(Date.now()-18e6),notes:"学习了语义化标签"}],B0={lastOpenedDirectory:"/Users/<USER>/Documents/学习资料",videoPlaybackSpeed:"1",theme:"light"},U0=Object.freeze(Object.defineProperty({__proto__:null,mockFiles:Fd,mockProgress:$0,mockSettings:B0,mockSubFiles:Ad},Symbol.toStringTag,{value:"Module"})),H0={readDirectory:async e=>(console.log("Mock readDirectory:",e),await new Promise(t=>setTimeout(t,300)),e==="/Users/<USER>/Documents/学习资料"||!e?Fd:Ad[e]||[]),selectDirectory:async()=>(console.log("Mock selectDirectory"),await new Promise(e=>setTimeout(e,500)),"/Users/<USER>/Documents/学习资料"),openExternal:async e=>{console.log("Mock openExternal:",e),e.startsWith("http")?window.open(e,"_blank"):alert(`模拟打开外部程序: ${e}`)},showItemInFolder:async e=>{console.log("Mock showItemInFolder:",e),alert(`模拟在文件夹中显示: ${e}`)},minimizeWindow:async()=>{console.log("Mock minimizeWindow"),alert("模拟最小化窗口")},maximizeWindow:async()=>{console.log("Mock maximizeWindow"),alert("模拟最大化窗口")},closeWindow:async()=>{console.log("Mock closeWindow"),confirm("确定要关闭窗口吗？")&&window.close()},getAppVersion:async()=>"1.0.0-mock",getPlatform:async()=>navigator.platform.toLowerCase().includes("mac")?"darwin":navigator.platform.toLowerCase().includes("win")?"win32":"linux"},W0=()=>{typeof window<"u"&&!window.electronAPI&&(window.electronAPI=H0,console.log("🎭 Mock Electron APIs initialized for browser development"))};function Q0(){const[e,t]=x.useState(null),[n,r]=x.useState(null),[o,l]=x.useState(null),[s,i]=x.useState(!1),[a,c]=x.useState(0),[g,h]=x.useState([]),[p,y]=x.useState(-1);x.useEffect(()=>{k()},[]);const k=async()=>{try{W0(),await G.initialize();const v=await G.getSetting("lastOpenedDirectory");v&&t(v)}catch(v){console.error("Failed to initialize app:",v)}},w=v=>{t(v),r(null)},E=v=>{r(v),C(v),e&&S(e,v)},f=[".DS_Store","Thumbs.db","desktop.ini",".localized",".fseventsd",".Spotlight-V100",".Trashes",".TemporaryItems",".DocumentRevisions-V100",".VolumeIcon.icns",".com.apple.timemachine.donotpresent",".AppleDouble",".LSOverride",".AppleDB",".AppleDesktop","Network Trash Folder","Temporary Items",".apdisk"],d=v=>v.startsWith(".")?![".gitignore",".env",".env.example",".editorconfig",".prettierrc",".eslintrc"].some(R=>v.startsWith(R)):f.includes(v),m=async v=>{try{const R=(await window.electronAPI.readDirectory(v)).filter(X=>!d(X.name));let H=[];for(const X of R)if(X.isDirectory){const I=await m(X.path);H=H.concat(I)}else Ma(X.extension)!=="other"&&H.push(X);return H}catch(_){return console.error("Failed to get viewable files:",_),[]}},S=async(v,_)=>{try{const H=(await m(v)).sort((I,O)=>I.path.localeCompare(O.path));h(H);const X=H.findIndex(I=>I.path===_.path);y(X)}catch(R){console.error("Failed to load file list for navigation:",R)}},N=()=>{if(p>0){const v=g[p-1];r(v),y(p-1),C(v)}},z=()=>{if(p<g.length-1){const v=g[p+1];r(v),y(p+1),C(v)}},C=async v=>{try{const _=await G.getLearningProgress(v.path);l(_)}catch(_){console.error("Failed to load file progress:",_),l(null)}},D=v=>{l(v),c(_=>_+1)},A=()=>{if(!n)return u.jsx("div",{className:"h-full flex items-center justify-center text-muted-foreground",children:u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-6xl mb-4",children:"📁"}),u.jsx("div",{className:"text-xl mb-2",children:"选择一个文件开始学习"}),u.jsx("div",{className:"text-sm",children:"支持视频、PDF、图片和文本文件"})]})});switch(Ma(n.extension)){case"video":return u.jsx(I0,{file:n,onProgressUpdate:D});case"pdf":return u.jsx(R0,{file:n,onProgressUpdate:D});case"image":return u.jsx(F0,{file:n,onProgressUpdate:D});case"text":return u.jsx(A0,{file:n,onProgressUpdate:D});default:return u.jsx("div",{className:"h-full flex items-center justify-center text-muted-foreground",children:u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-4xl mb-4",children:"📄"}),u.jsx("div",{className:"text-lg mb-2",children:"不支持的文件类型"}),u.jsx("div",{className:"text-sm",children:n.name}),u.jsx("div",{className:"text-xs mt-2",children:"支持的格式：视频 (mp4, avi, mov 等)、PDF、图片 (jpg, png 等)、文本 (txt, md 等)"})]})})}};return u.jsxs("div",{className:"h-screen flex flex-col bg-background text-foreground",children:[u.jsx("div",{className:"flex-1 min-h-0",children:u.jsx(V0,{leftPanel:u.jsx(o0,{currentPath:e,onPathChange:w,onFileSelect:E,selectedFile:n,refreshTrigger:a}),rightPanel:u.jsxs("div",{className:"flex-container-full",children:[u.jsx("div",{className:"flex-shrink-0 border-b bg-card",children:u.jsxs("div",{className:"h-12 flex items-center justify-between px-4",children:[u.jsx("div",{className:"flex items-center gap-2 flex-1 min-w-0",children:n&&u.jsxs(u.Fragment,{children:[u.jsx("span",{className:"font-medium truncate",children:n.name}),o&&u.jsxs("div",{className:"flex items-center gap-2 ml-4",children:[u.jsxs("div",{className:"text-sm text-muted-foreground",children:["进度: ",Math.round(o.progress),"%"]}),(o.isCompleted||o.isMarkedAsLearned)&&u.jsx("span",{className:"text-green-600 text-sm",children:"✓ 已学习"})]})]})}),u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(b0,{onPrevious:N,onNext:z,hasPrevious:p>0,hasNext:p<g.length-1}),u.jsx("div",{className:"w-px h-6 bg-border mx-1"}),u.jsx(Z,{variant:"ghost",size:"icon",onClick:()=>i(!0),className:"h-8 w-8",children:u.jsx(od,{className:"w-4 h-4"})})]})]})}),u.jsx("div",{className:"flex-1 min-h-0",children:A()})]})})}),u.jsx(O0,{isOpen:s,onClose:()=>i(!1),onSettingsChange:()=>{n&&C(n)}})]})}Dl.createRoot(document.getElementById("root")).render(u.jsx(xe.StrictMode,{children:u.jsx(Q0,{})}));
