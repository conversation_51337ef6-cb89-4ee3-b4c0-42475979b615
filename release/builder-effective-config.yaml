directories:
  output: release
  buildResources: build
appId: com.localbrowser.app
productName: Local Browser
files:
  - filter:
      - dist/**/*
      - node_modules/**/*
      - '!node_modules/.cache/**/*'
nodeGypRebuild: false
buildDependenciesFromSource: false
npmRebuild: false
mac:
  category: public.app-category.education
  target:
    - target: dmg
      arch:
        - x64
        - arm64
win:
  target: nsis
linux:
  target: AppImage
electronVersion: 28.3.3
